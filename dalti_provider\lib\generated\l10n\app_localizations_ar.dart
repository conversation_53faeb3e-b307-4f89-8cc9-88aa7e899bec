/// Generated file. Do not edit.
///
/// Original file: lib/l10n/app_en.arb
/// To regenerate, run: `flutter gen-l10n`
///
/// Localization for Dalti Provider App
/// Supports: English (en), French (fr), Arabic (ar)

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'دالتي للمزودين';

  @override
  String get language => 'اللغة';

  @override
  String get languageEnglish => 'English';

  @override
  String get languageFrench => 'Français';

  @override
  String get languageArabic => 'العربية';

  @override
  String get settings => 'الإعدادات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get appointments => 'المواعيد';

  @override
  String get appointmentDetails => 'تفاصيل الموعد';

  @override
  String get calendar => 'التقويم';

  @override
  String get messages => 'الرسائل';

  @override
  String get customers => 'عملاء';

  @override
  String get services => 'الخدمات';

  @override
  String get locations => 'المواقع';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get ok => 'موافق';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get languageChanged => 'تم تغيير اللغة بنجاح';

  @override
  String get languageChangeError =>
      'فشل في تغيير اللغة. يرجى المحاولة مرة أخرى.';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get currentLanguage => 'اللغة الحالية';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get register => 'التسجيل';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get firstName => 'الاسم الأول';

  @override
  String get lastName => 'اسم العائلة';

  @override
  String get phone => 'الهاتف';

  @override
  String get businessName => 'اسم العمل التجاري';

  @override
  String get add => 'إضافة';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get update => 'تحديث';

  @override
  String get create => 'إنشاء';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get refresh => 'تحديث';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get close => 'إغلاق';

  @override
  String get done => 'تم';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get noData => 'لا توجد بيانات متاحة';

  @override
  String get networkError => 'خطأ في الشبكة. يرجى التحقق من اتصالك.';

  @override
  String get serverError => 'خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.';

  @override
  String get unknownError => 'حدث خطأ غير معروف.';

  @override
  String get validationError => 'يرجى التحقق من إدخالك والمحاولة مرة أخرى.';

  @override
  String get required => 'هذا الحقل مطلوب';

  @override
  String get invalidEmail => 'يرجى إدخال عنوان بريد إلكتروني صحيح';

  @override
  String get invalidPhone => 'يرجى إدخال رقم هاتف صحيح';

  @override
  String get passwordTooShort =>
      'يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get helpAndSupport => 'المساعدة والدعم';

  @override
  String get theme => 'المظهر';

  @override
  String get themeLight => 'فاتح';

  @override
  String get themeDark => 'داكن';

  @override
  String get themeSystem => 'النظام';

  @override
  String get logoutConfirmTitle => 'تسجيل الخروج';

  @override
  String get logoutConfirmMessage => 'هل أنت متأكد من أنك تريد تسجيل الخروج؟';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get daltiProvider => 'دالتي للمقدمين';

  @override
  String get gettingLocation => 'جاري الحصول على الموقع...';

  @override
  String get useCurrentLocation => 'استخدام الموقع الحالي';

  @override
  String get notSpecified => 'غير محدد';

  @override
  String get title => 'المسمى الوظيفي';

  @override
  String get category => 'الفئة';

  @override
  String get profileInformation => 'معلومات الملف الشخصي';

  @override
  String get aboutMe => 'نبذة عني';

  @override
  String get passwordResetSuccessful => 'تم إعادة تعيين كلمة المرور بنجاح';

  @override
  String get passwordResetSuccessMessage =>
      'تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.';

  @override
  String get goToLogin => 'الذهاب لتسجيل الدخول';

  @override
  String get failedToResetPassword =>
      'فشل في إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.';

  @override
  String get forgotPassword => 'نسيت كلمة المرور';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmNewPassword => 'تأكيد كلمة المرور الجديدة';

  @override
  String get enterOtp => 'أدخل رمز التحقق';

  @override
  String get verifyOtp => 'تحقق من الرمز';

  @override
  String get resendOtp => 'إعادة إرسال الرمز';

  @override
  String get otpSent => 'تم إرسال رمز التحقق إلى بريدك الإلكتروني';

  @override
  String get invalidOtp => 'رمز التحقق غير صحيح. يرجى المحاولة مرة أخرى.';

  @override
  String get welcomeBack => 'مرحباً بعودتك';

  @override
  String get signInToContinue => 'سجل الدخول للمتابعة';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get alreadyHaveAccount => 'هل لديك حساب بالفعل؟';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get appointmentsList => 'المواعيد';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get loginSuccessful => 'تم تسجيل الدخول بنجاح!';

  @override
  String get loginFailed => 'فشل تسجيل الدخول';

  @override
  String get welcomeToDaltiProvider => 'مرحباً بك في دالتي للمزودين';

  @override
  String get signInToManageBusiness => 'سجل الدخول لإدارة عملك';

  @override
  String get getStarted => 'ابدأ الآن';

  @override
  String get skipSetup => 'تخطي الإعداد؟';

  @override
  String get skipSetupMessage =>
      'يمكنك إكمال إعداد عملك لاحقاً من لوحة التحكم. ومع ذلك، قد تكون بعض الميزات محدودة حتى يتم إكمال الإعداد.';

  @override
  String get continueSetup => 'متابعة الإعداد';

  @override
  String get skipForNow => 'تخطي الآن';

  @override
  String get startConversation => 'بدء المحادثة';

  @override
  String get failedToCreateConversation => 'فشل في إنشاء المحادثة';

  @override
  String get selectCustomer => 'اختر عميل';

  @override
  String get initialMessage => 'الرسالة الأولى (اختيارية):';

  @override
  String get typeMessageHere => 'اكتب رسالتك هنا...';

  @override
  String get serviceColor => 'لون الخدمة';

  @override
  String get customerManagement => 'إدارة العملاء';

  @override
  String get comingSoon => 'قريباً';

  @override
  String get thisFeatureWillAllowYouTo => 'ستتيح لك هذه الميزة:';

  @override
  String get basicInformation => 'المعلومات الأساسية';

  @override
  String get contactInformation => 'معلومات الاتصال';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get nationalId => 'رقم الهوية الوطنية';

  @override
  String get notes => 'ملاحظات';

  @override
  String get firstNameRequired => 'الاسم الأول مطلوب';

  @override
  String get lastNameRequired => 'اسم العائلة مطلوب';

  @override
  String get pleaseEnterValidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get pleaseEnterValidPhone => 'يرجى إدخال رقم هاتف صحيح';

  @override
  String get customerEmailHint => 'عميل@مثال.com';

  @override
  String get phoneNumberHint => '+213 123 456 789';

  @override
  String get nationalIdHint => 'رقم هوية اختياري';

  @override
  String get notesHint => 'ملاحظات إضافية حول العميل';

  @override
  String get activeCustomer => 'عميل نشط';

  @override
  String get inactiveCustomer => 'عميل غير نشط';

  @override
  String get blockedCustomer => 'عميل محظور';

  @override
  String get serviceTitle => 'عنوان الخدمة';

  @override
  String get serviceTitleHint => 'مثال: استشارة، قص شعر، تدليك';

  @override
  String get serviceTitleRequired => 'عنوان الخدمة مطلوب';

  @override
  String get titleMinLength => 'يجب أن يكون العنوان على الأقل حرفين';

  @override
  String get creditPointsRequired => 'نقاط الائتمان المطلوبة';

  @override
  String get creditPointsHint =>
      'النقاط المطلوبة لحجز هذه الخدمة (الحد الأدنى 1)';

  @override
  String get creditPointsRequiredError => 'متطلبات نقاط الائتمان مطلوبة';

  @override
  String get creditPointsPositive => 'يجب أن تكون نقاط الائتمان رقماً موجباً';

  @override
  String get deleteService => 'حذف الخدمة';

  @override
  String deleteServiceConfirm(String serviceName) {
    return 'هل أنت متأكد من أنك تريد حذف \"$serviceName\"؟\n\nلا يمكن التراجع عن هذا الإجراء.';
  }

  @override
  String get activate => 'تفعيل';

  @override
  String get deactivate => 'إلغاء تفعيل';

  @override
  String get minutes => 'دقائق';

  @override
  String serviceDeletedSuccessfully(String serviceName) {
    return 'تم حذف الخدمة \"$serviceName\" بنجاح';
  }

  @override
  String failedToDeleteService(String serviceName) {
    return 'فشل في حذف الخدمة \"$serviceName\"';
  }

  @override
  String get priceNotSet => 'لم يتم تحديد السعر';

  @override
  String get active => 'نشط';

  @override
  String requiresCreditPoints(int points) {
    return 'يتطلب $points نقاط ائتمان للحجز';
  }

  @override
  String get hours => 'س';

  @override
  String get inactive => 'غير نشط';

  @override
  String get queue => 'طابور';

  @override
  String get queues => 'طوابير';

  @override
  String queueCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'طوابير',
      one: 'طابور',
    );
    return '$count $_temp0';
  }

  @override
  String get errorLoadingQueues => 'خطأ في تحميل الطوابير';

  @override
  String get createLocationsFirstMessage =>
      'أنشئ المواقع أولاً لإدارة الطوابير.';

  @override
  String get noQueuesFound => 'لم يتم العثور على طوابير';

  @override
  String get noQueuesForLocation => 'لم يتم العثور على طوابير للموقع المحدد.';

  @override
  String get createFirstQueue => 'أنشئ طابورك الأول للبدء.';

  @override
  String get addQueue => 'إضافة طابور';

  @override
  String get deleteQueue => 'حذف الطابور';

  @override
  String deleteQueueConfirmation(String queueName) {
    return 'هل أنت متأكد من أنك تريد حذف الطابور \"$queueName\"؟\\n\\nلا يمكن التراجع عن هذا الإجراء.';
  }

  @override
  String customerCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'عملاء',
      one: 'عميل',
    );
    return '$count $_temp0';
  }

  @override
  String get clearFilters => 'مسح المرشحات';

  @override
  String get manageCustomerProfiles => 'إدارة ملفات العملاء';

  @override
  String get trackCustomerHistory => 'تتبع تاريخ العملاء';

  @override
  String get sendNotifications => 'إرسال الإشعارات';

  @override
  String get customerPreferences => 'تفضيلات العملاء';

  @override
  String get loyaltyPrograms => 'برامج الولاء';

  @override
  String get call => 'اتصال';

  @override
  String get sms => 'رسالة نصية';

  @override
  String get book => 'حجز';

  @override
  String get editCustomer => 'تعديل العميل';

  @override
  String get status => 'الحالة';

  @override
  String get wilaya => 'الولاية';

  @override
  String get dateAdded => 'تاريخ الإضافة';

  @override
  String get minimumAppointments => 'الحد الأدنى للمواعيد';

  @override
  String get minimumSpent => 'الحد الأدنى للإنفاق (دج)';

  @override
  String get selectWilaya => 'اختر الولاية';

  @override
  String get applyFilters => 'تطبيق المرشحات';

  @override
  String get clearAll => 'مسح الكل';

  @override
  String get searchCustomersPlaceholder =>
      'البحث بالاسم أو البريد الإلكتروني أو الهاتف...';

  @override
  String get newCustomer => 'عميل جديد';

  @override
  String get additionalInformation => 'معلومات إضافية';

  @override
  String get additionalNotesHint => 'ملاحظات إضافية حول العميل';

  @override
  String get customerAlreadyExists => 'العميل موجود بالفعل';

  @override
  String get customerExistsInArchive =>
      'يوجد عميل بهذه المعلومات بالفعل في الأرشيف.';

  @override
  String get wouldYouLikeToRestore =>
      'هل تريد استعادة العميل الموجود بدلاً من ذلك؟';

  @override
  String get restoreCustomer => 'استعادة العميل';

  @override
  String customerRestoredSuccessfully(String firstName, String lastName) {
    return 'تم استعادة العميل $firstName $lastName بنجاح';
  }

  @override
  String failedToRestoreCustomer(String error) {
    return 'فشل في استعادة العميل: $error';
  }

  @override
  String customerDeletedSuccessfully(String firstName, String lastName) {
    return 'تم حذف العميل $firstName $lastName بنجاح';
  }

  @override
  String failedToDeleteCustomer(String error) {
    return 'فشل في حذف العميل: $error';
  }

  @override
  String customerUpdatedSuccessfully(String firstName, String lastName) {
    return 'تم تحديث العميل $firstName $lastName بنجاح';
  }

  @override
  String failedToUpdateCustomer(String error) {
    return 'فشل في تحديث العميل: $error';
  }

  @override
  String get searchAppointmentsPlaceholder => 'البحث في المواعيد...';

  @override
  String get filters => 'المرشحات';

  @override
  String get all => 'الكل';

  @override
  String get filterAppointments => 'تصفية المواعيد';

  @override
  String get customizeAppointmentView => 'خصص عرض المواعيد باستخدام المرشحات';

  @override
  String get queueFilter => 'مرشح الطابور';

  @override
  String get pending => 'في الانتظار';

  @override
  String get scheduled => 'مجدول';

  @override
  String get confirmed => 'مؤكد';

  @override
  String get inProgress => 'قيد التنفيذ';

  @override
  String get completed => 'مكتمل';

  @override
  String get canceled => 'ملغي';

  @override
  String get noShow => 'لم يحضر';

  @override
  String get rescheduled => 'معاد الجدولة';

  @override
  String get confirm => 'تأكيد';

  @override
  String get startService => 'بدء الخدمة';

  @override
  String get complete => 'مكتمل';

  @override
  String get editAppointment => 'تعديل الموعد';

  @override
  String get customerDidNotShowUp => 'لم يحضر العميل للموعد';

  @override
  String get sessionCompletedSuccessfully => 'تم إنهاء الجلسة بنجاح';

  @override
  String get dateRange => 'نطاق التاريخ';

  @override
  String get startDate => 'تاريخ البداية';

  @override
  String get endDate => 'تاريخ النهاية';

  @override
  String get statusFilter => 'مرشح الحالة';

  @override
  String get quickSelection => 'اختيار سريع';

  @override
  String get today => 'اليوم';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get customRange => 'نطاق مخصص';

  @override
  String get selectStartDate => 'اختر تاريخ البداية';

  @override
  String get selectEndDate => 'اختر تاريخ النهاية';

  @override
  String get clear => 'مسح';

  @override
  String get statusLabel => 'الحالة';

  @override
  String get dateRangeLabel => 'نطاق التاريخ';

  @override
  String get queueLabel => 'طابور';

  @override
  String get any => 'أي';

  @override
  String get customerInformation => 'معلومات العميل';

  @override
  String get serviceDetails => 'تفاصيل الخدمة';

  @override
  String get scheduling => 'الجدولة';

  @override
  String get customerRequired => 'العميل *';

  @override
  String get serviceRequired => 'الخدمة *';

  @override
  String get locationRequired => 'الموقع *';

  @override
  String get queueRequired => 'الطابور *';

  @override
  String get dateRequired => 'التاريخ *';

  @override
  String get timeRangeRequired => 'النطاق الزمني *';

  @override
  String get selectService => 'اختر خدمة';

  @override
  String get selectLocation => 'اختر موقع';

  @override
  String get selectQueue => 'اختر طابور';

  @override
  String get selectLocationFirst => 'اختر موقع أولاً';

  @override
  String get appointmentDate => 'تاريخ الموعد';

  @override
  String get startTime => 'وقت البداية';

  @override
  String get endTime => 'وقت النهاية';

  @override
  String get notesOptional => 'ملاحظات (اختياري)';

  @override
  String get addNotesPlaceholder => 'أضف أي ملاحظات إضافية...';

  @override
  String get pleaseSelectCustomer => 'يرجى اختيار عميل';

  @override
  String get pleaseSelectService => 'يرجى اختيار خدمة';

  @override
  String get pleaseSelectLocation => 'يرجى اختيار موقع';

  @override
  String get pleaseSelectQueue => 'يرجى اختيار طابور';

  @override
  String get startTimeBeforeEndTime =>
      'يجب أن يكون وقت البداية قبل وقت النهاية';

  @override
  String get appointmentCreatedSuccessfully => 'تم إنشاء الموعد بنجاح';

  @override
  String get appointmentNumber => 'الموعد #';

  @override
  String get customerLabel => 'العميل';

  @override
  String get serviceLabel => 'الخدمة';

  @override
  String get originalTime => 'الوقت الأصلي';

  @override
  String get selectStartTime => 'اختر وقت البداية';

  @override
  String get selectEndTime => 'اختر وقت النهاية';

  @override
  String get pleaseSelectLocationFirst => 'يرجى اختيار موقع أولاً';

  @override
  String get noQueuesAvailable => 'لا توجد طوابير متاحة للموقع المحدد';

  @override
  String get errorLoadingLocations => 'خطأ في تحميل المواقع';

  @override
  String get appointmentNotesOptional => 'ملاحظات الموعد (اختياري)';

  @override
  String get duration => 'المدة';

  @override
  String get time => 'الوقت';

  @override
  String get statusScheduled => 'مجدول';

  @override
  String get statusConfirmed => 'مؤكد';

  @override
  String get statusCompleted => 'مكتمل';

  @override
  String get statusCanceled => 'ملغي';

  @override
  String get statusNoShow => 'لم يحضر';

  @override
  String get cancelAppointment => 'إلغاء الموعد';

  @override
  String get cancelAppointmentConfirmation =>
      'هل أنت متأكد من أنك تريد إلغاء هذا الموعد؟';

  @override
  String get cancellationReason => 'سبب الإلغاء (اختياري)';

  @override
  String get cancellationReasonHint => 'أدخل سبب الإلغاء...';

  @override
  String get keepAppointment => 'الاحتفاظ بالموعد';

  @override
  String get appointmentCancelledSuccessfully => 'تم إلغاء الموعد بنجاح';

  @override
  String get failedToCancelAppointment => 'فشل في إلغاء الموعد';

  @override
  String get calendarSettings => 'إعدادات التقويم';

  @override
  String get configureCalendarDisplayPreferences => 'تكوين تفضيلات عرض التقويم';

  @override
  String get timeSlotInterval => 'فترة الفترات الزمنية';

  @override
  String get selectInterval => 'اختر الفترة';

  @override
  String get timeSlotIntervalDescription =>
      'يحدد هذا الإعداد عدد الفترات الزمنية التي تنقسم إليها كل ساعة. على سبيل المثال، 5 دقائق ستنشئ 12 فترة في الساعة، بينما 15 دقيقة تنشئ 4 فترات في الساعة.';

  @override
  String get apply => 'تطبيق';

  @override
  String get statusAndOverview => 'الحالة والنظرة العامة';

  @override
  String get serviceInformation => 'معلومات الخدمة';

  @override
  String get primaryLocation => 'الموقع الأساسي';

  @override
  String get cancelAppointmentConfirm => 'إلغاء الموعد';

  @override
  String cancelAppointmentMessage(String customerName) {
    return 'هل أنت متأكد من أنك تريد إلغاء الموعد مع $customerName؟';
  }

  @override
  String get completeAppointmentTitle => 'إكمال الموعد';

  @override
  String completeAppointmentMessage(String customerName) {
    return 'تحديد الموعد مع $customerName كمكتمل؟';
  }

  @override
  String appointmentConfirmedFor(String customerName) {
    return 'تم تأكيد الموعد لـ $customerName';
  }

  @override
  String appointmentCanceledFor(String customerName) {
    return 'تم إلغاء الموعد مع $customerName';
  }

  @override
  String get customer => 'العميل';

  @override
  String get service => 'الخدمة';

  @override
  String get location => 'الموقع';

  @override
  String get profileOverview => 'نظرة عامة على الملف الشخصي';

  @override
  String get statistics => 'الإحصائيات';

  @override
  String get rating => 'التقييم';

  @override
  String get reviews => 'المراجعات';

  @override
  String get setupStatus => 'حالة الإعداد';

  @override
  String get incomplete => 'غير مكتمل';

  @override
  String get businessLogo => 'شعار العمل';

  @override
  String get provider => 'مزود الخدمة';

  @override
  String get verified => 'موثق';

  @override
  String get changeLogo => 'تغيير الشعار';

  @override
  String get uploadLogo => 'رفع الشعار';

  @override
  String get supportedFormats =>
      'الصيغ المدعومة: PNG, JPG, JPEG • الحد الأقصى: 5 ميجابايت';

  @override
  String get noLogoUploaded => 'لم يتم رفع شعار';

  @override
  String get uploadBusinessLogo => 'رفع شعار الأعمال';

  @override
  String get errorLoadingProfile => 'خطأ في تحميل الملف الشخصي';

  @override
  String get profileAndBusinessImages => 'صور الملف الشخصي والأعمال';

  @override
  String get professionalTitle => 'المسمى المهني';

  @override
  String get actions => 'الإجراءات';

  @override
  String get profilePicture => 'صورة الملف الشخصي';

  @override
  String get titleRequired => 'المسمى مطلوب';

  @override
  String get phoneRequired => 'رقم الهاتف مطلوب';

  @override
  String get noCategorySelected => 'لم يتم اختيار فئة';

  @override
  String get categoryCannotBeChanged => 'لا يمكن تغيير الفئة';

  @override
  String get saving => 'جاري الحفظ...';

  @override
  String get saveChanges => 'حفظ التغييرات';

  @override
  String get profileUpdatedSuccessfully => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String failedToUpdateProfile(String error) {
    return 'فشل في تحديث الملف الشخصي: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully =>
      'تم تحديث صورة الملف الشخصي بنجاح!';

  @override
  String get profileCompletion => 'اكتمال الملف الشخصي';

  @override
  String get overallProgress => 'التقدم العام';

  @override
  String percentComplete(int percentage) {
    return '$percentage% مكتمل';
  }

  @override
  String get details => 'التفاصيل';

  @override
  String get profilePictureItem => 'صورة الملف الشخصي';

  @override
  String get providerInfo => 'معلومات المزود';

  @override
  String get nextSteps => 'الخطوات التالية';

  @override
  String get failedToLoadProfileCompletion =>
      'فشل في تحميل اكتمال الملف الشخصي';

  @override
  String get uploadProfessionalProfilePicture => 'رفع صورة ملف شخصي مهنية';

  @override
  String get completeProviderInformation => 'إكمال معلومات المزود: وصف العمل';

  @override
  String get addAtLeastOneServiceLocation => 'إضافة موقع خدمة واحد على الأقل';

  @override
  String get createAtLeastOneServiceOffering => 'إنشاء عرض خدمة واحد على الأقل';

  @override
  String get setUpBookingQueuesTimeSlots =>
      'إعداد طوابير الحجز/الفترات الزمنية';

  @override
  String get customerProfile => 'ملف العميل';

  @override
  String get deleteCustomer => 'حذف العميل';

  @override
  String deleteCustomerConfirmation(String firstName, String lastName) {
    return 'هل أنت متأكد من أنك تريد حذف $firstName $lastName؟ لا يمكن التراجع عن هذا الإجراء.';
  }

  @override
  String get information => 'المعلومات';

  @override
  String get mobile => 'الهاتف المحمول';

  @override
  String get customerSince => 'عميل منذ';

  @override
  String get customerStatistics => 'إحصائيات العميل';

  @override
  String get totalSpent => 'إجمالي المبلغ المنفق';

  @override
  String get lastVisit => 'آخر زيارة';

  @override
  String get never => 'أبداً';

  @override
  String get recentAppointments => 'المواعيد الأخيرة';

  @override
  String get noRecentAppointments => 'لا توجد مواعيد حديثة';

  @override
  String get addAppointmentFeatureComingSoon =>
      'ميزة إضافة الموعد قادمة قريباً';

  @override
  String get loadingDashboard => 'جاري تحميل لوحة التحكم...';

  @override
  String get failedToLoadDashboard => 'فشل في تحميل لوحة التحكم';

  @override
  String get unknownErrorOccurred => 'حدث خطأ غير معروف';

  @override
  String get refreshingData => 'جاري تحديث البيانات...';

  @override
  String get failedToLoadActiveSessions => 'فشل في تحميل الجلسات النشطة';

  @override
  String get failedToLoadPendingAppointments => 'فشل في تحميل المواعيد المعلقة';

  @override
  String get processingEmergencyControl => 'جاري معالجة التحكم الطارئ...';

  @override
  String get refreshService => 'تحديث الخدمة';

  @override
  String get editService => 'تعديل الخدمة';

  @override
  String get locationName => 'اسم الموقع';

  @override
  String get locationNameHint => 'مثال: المكتب الرئيسي، فرع وسط المدينة';

  @override
  String get locationNameRequired => 'اسم الموقع مطلوب';

  @override
  String get locationNameMinLength => 'يجب أن يكون اسم الموقع على الأقل حرفين';

  @override
  String get locationNameMaxLength => 'يجب أن يكون اسم الموقع أقل من 100 حرف';

  @override
  String get streetAddress => 'عنوان الشارع';

  @override
  String get streetAddressHint => 'مثل: 123 الشارع الرئيسي';

  @override
  String get streetAddressRequired => 'عنوان الشارع مطلوب';

  @override
  String get pleaseEnterCompleteAddress => 'يرجى إدخال عنوان كامل';

  @override
  String get addressMaxLength => 'يجب أن يكون العنوان أقل من 200 حرف';

  @override
  String get country => 'البلد';

  @override
  String get algeria => 'الجزائر';

  @override
  String get monday => 'الاثنين';

  @override
  String get tuesday => 'الثلاثاء';

  @override
  String get wednesday => 'الأربعاء';

  @override
  String get thursday => 'الخميس';

  @override
  String get friday => 'الجمعة';

  @override
  String get saturday => 'السبت';

  @override
  String get sunday => 'الأحد';

  @override
  String get activeServiceSessions => 'جلسات الخدمة النشطة';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get noActiveSessions => 'لا توجد جلسات نشطة';

  @override
  String get noActiveSessionsDescription =>
      'لا توجد جلسات خدمة نشطة في الوقت الحالي';

  @override
  String get pendingAppointments => 'المواعيد المعلقة';

  @override
  String get allCaughtUp => 'كل شيء محدث!';

  @override
  String get noPendingAppointments => 'لا توجد مواعيد معلقة للمراجعة';

  @override
  String get todaysSchedule => 'جدول اليوم';

  @override
  String get unableToRefreshData =>
      'غير قادر على تحديث البيانات. عرض المعلومات المحفوظة مؤقتاً.';

  @override
  String get goodMorning => 'صباح الخير';

  @override
  String get goodAfternoon => 'مساء الخير';

  @override
  String get goodEvening => 'مساء الخير';

  @override
  String get waiting => 'في الانتظار';

  @override
  String moreQueues(int count) {
    return '+$count طوابير إضافية';
  }

  @override
  String get noActiveQueues => 'لا توجد طوابير نشطة';

  @override
  String get total => 'المجموع';

  @override
  String get upcoming => 'قادم';

  @override
  String appointmentsAwaitingAction(int count, String plural) {
    return '$count موعد$plural في انتظار الإجراء';
  }

  @override
  String get quickActions => 'الإجراءات السريعة';

  @override
  String get newService => 'خدمة جديدة';

  @override
  String get newQueue => 'طابور جديد';

  @override
  String get newAppointment => 'موعد جديد';

  @override
  String get noUpcomingAppointments => 'لا توجد مواعيد قادمة';

  @override
  String get allAppointmentsCompleted => 'جميع مواعيد اليوم مكتملة';

  @override
  String get todaysSummary => 'ملخص اليوم';

  @override
  String updatedSecondsAgo(int seconds) {
    return 'تم التحديث منذ $secondsث';
  }

  @override
  String updatedMinutesAgo(int minutes) {
    return 'تم التحديث منذ $minutesد';
  }

  @override
  String updatedHoursAgo(int hours) {
    return 'تم التحديث منذ $hoursس';
  }

  @override
  String updatedDaysAgo(int days) {
    return 'تم التحديث منذ $days أيام';
  }

  @override
  String get justNow => 'الآن';

  @override
  String get dataRefresh => 'تحديث البيانات';

  @override
  String lastUpdated(String timeAgo) {
    return 'آخر تحديث: $timeAgo';
  }

  @override
  String get byLocation => 'حسب الموقع';

  @override
  String get allQueues => 'جميع الطوابير';

  @override
  String get scheduleManagement => 'إدارة الجداول';

  @override
  String get weeklyView => 'العرض الأسبوعي';

  @override
  String get listView => 'عرض القائمة';

  @override
  String get appSettings => 'إعدادات التطبيق';

  @override
  String get account => 'الحساب';

  @override
  String get management => 'الإدارة';

  @override
  String get manageServiceOfferings => 'إدارة عروض الخدمات الخاصة بك';

  @override
  String get configureBusinessLocations => 'تكوين مواقع الأعمال';

  @override
  String get setupAppointmentQueues => 'إعداد طوابير المواعيد';

  @override
  String get getHelpAndSupport => 'احصل على المساعدة والدعم';

  @override
  String get signOutOfAccount => 'تسجيل الخروج من حسابك';

  @override
  String get queueManagement => 'إدارة الطوابير';

  @override
  String get createService => 'إنشاء خدمة';

  @override
  String get createNewService => 'إنشاء خدمة جديدة';

  @override
  String get addNewServiceDescription => 'أضف خدمة جديدة إلى عروضك التجارية';

  @override
  String get serviceAvailableInfo =>
      'ستكون خدمتك متاحة للحجز بمجرد إنشائها. يمكنك تعديلها أو تعطيلها لاحقاً من قائمة الخدمات.';

  @override
  String get creatingService => 'جاري إنشاء الخدمة...';

  @override
  String get description => 'الوصف';

  @override
  String get describeYourService => 'اوصف خدمتك';

  @override
  String get minutesShort => 'د';

  @override
  String get hoursShort => 'س';

  @override
  String get createLocation => 'إنشاء موقع';

  @override
  String get nameMinLength => 'يجب أن يكون الاسم على الأقل حرفين';

  @override
  String get nameMaxLength => 'يجب أن يكون الاسم أقل من 100 حرف';

  @override
  String get shortName => 'الاسم المختصر';

  @override
  String get shortNameHint => 'مثال: المكتب الرئيسي';

  @override
  String get shortNameMaxLength => 'يجب أن يكون الاسم المختصر أقل من 100 حرف';

  @override
  String get city => 'المدينة';

  @override
  String get cityRequired => 'المدينة مطلوبة';

  @override
  String get selectValidCity => 'يرجى اختيار مدينة جزائرية صحيحة';

  @override
  String get selectAlgerianCity => 'اختر مدينة جزائرية';

  @override
  String get countryRequired => 'البلد مطلوب';

  @override
  String get onlyAvailableInAlgeria => 'متاح حالياً في الجزائر فقط';

  @override
  String locationCreatedSuccessfully(String locationName) {
    return 'تم إنشاء الموقع \"$locationName\" بنجاح';
  }

  @override
  String get failedToCreateLocation => 'فشل في إنشاء الموقع';

  @override
  String locationUpdatedSuccessfully(String locationName) {
    return 'تم تحديث الموقع \"$locationName\" بنجاح';
  }

  @override
  String get failedToUpdateLocation => 'فشل في تحديث الموقع';

  @override
  String get createNewLocation => 'إنشاء موقع جديد';

  @override
  String get addNewBusinessLocation =>
      'إضافة موقع عمل جديد مع العنوان وساعات العمل';

  @override
  String get locationInformation => 'معلومات الموقع';

  @override
  String get address => 'العنوان';

  @override
  String get addressHint => 'مثال: 123 الشارع الرئيسي، المبنى أ';

  @override
  String get addressRequired => 'العنوان مطلوب';

  @override
  String get postalCode => 'الرمز البريدي';

  @override
  String get postalCodeHint => 'مثال: 16000 (اختياري)';

  @override
  String get timezone => 'المنطقة الزمنية';

  @override
  String get timezoneRequired => 'المنطقة الزمنية مطلوبة';

  @override
  String get locationCoordinates => 'إحداثيات الموقع';

  @override
  String get getCurrentLocationDescription =>
      'احصل على موقعك الحالي لمساعدة العملاء في العثور عليك بسهولة';

  @override
  String get latitude => 'خط العرض';

  @override
  String get longitude => 'خط الطول';

  @override
  String get willBeFilledAutomatically => 'سيتم ملؤه تلقائياً';

  @override
  String get pleaseGetCurrentLocation => 'يرجى الحصول على الموقع الحالي';

  @override
  String get invalidLatitude => 'خط عرض غير صحيح';

  @override
  String get invalidLongitude => 'خط طول غير صحيح';

  @override
  String get latitudeMustBeBetween => 'يجب أن يكون خط العرض بين -90 و 90';

  @override
  String get longitudeMustBeBetween => 'يجب أن يكون خط الطول بين -180 و 180';

  @override
  String get getCurrentLocation => 'الحصول على الموقع الحالي';

  @override
  String get amenities => 'المرافق';

  @override
  String get parkingAvailable => 'موقف سيارات متاح';

  @override
  String get onsiteParkingForCustomers => 'موقف سيارات في الموقع للعملاء';

  @override
  String get elevatorAccess => 'الوصول بالمصعد';

  @override
  String get buildingHasElevatorAccess => 'المبنى يحتوي على مصعد';

  @override
  String get wheelchairAccessible => 'يمكن الوصول إليه بالكرسي المتحرك';

  @override
  String get accessibleForPeopleWithDisabilities =>
      'يمكن الوصول إليه للأشخاص ذوي الإعاقة';

  @override
  String get openingHours => 'ساعات العمل';

  @override
  String get closed => 'مغلق';

  @override
  String get open => 'مفتوح';

  @override
  String get tipToggleSwitchDayOpenClosed =>
      'نصيحة: اضغط على المفتاح لتحديد اليوم كمفتوح أو مغلق';

  @override
  String get locationWillBeAddedToBusinessLocations =>
      'سيتم إضافة هذا الموقع إلى مواقع عملك. يمكنك إدارة جميع المواقع من قسم المواقع';

  @override
  String get locationAvailableForQueueManagement =>
      'سيكون موقعك متاحاً لإدارة الطوابير والمواعيد بمجرد إنشائه. تأكد من تحديد الإحداثيات الدقيقة وساعات العمل';

  @override
  String get creatingLocation => 'جاري إنشاء الموقع...';

  @override
  String get editLocation => 'تعديل الموقع';

  @override
  String get updateLocationDetails =>
      'تحديث تفاصيل الموقع والعنوان وساعات العمل';

  @override
  String get updateCoordinatesDescription =>
      'قم بتحديث الإحداثيات إذا انتقل الموقع لمساعدة العملاء في العثور عليك بسهولة';

  @override
  String get changesWillBeUpdatedAcrossServices =>
      'سيتم تحديث التغييرات على هذا الموقع عبر جميع خدماتك التجارية والطوابير';

  @override
  String get changesWillBeSavedImmediately =>
      'سيتم حفظ التغييرات فوراً وتطبيقها في إعدادات موقعك. الطوابير والمواعيد الحالية لن تتأثر';

  @override
  String get savingChanges => 'جاري حفظ التغييرات...';

  @override
  String get updateCurrentLocation => 'تحديث الموقع الحالي';

  @override
  String get fax => 'الفاكس';

  @override
  String get floor => 'الطابق';

  @override
  String get mobileHint => 'مثال: +213 555-123456';

  @override
  String get faxHint => 'مثال: +213 555-123456';

  @override
  String get floorHint => 'مثال: الطابق الخامس';

  @override
  String get createQueue => 'إنشاء الطابور';

  @override
  String get editQueue => 'تعديل الطابور';

  @override
  String get queueInformation => 'معلومات الطابور';

  @override
  String get queueName => 'اسم الطابور';

  @override
  String get queueNameHint =>
      'مثال: الطابور العام، طابور كبار الشخصيات، بدون موعد';

  @override
  String get queueServices => 'خدمات الطابور';

  @override
  String get selectServicesDescription =>
      'اختر الخدمات التي ستكون متاحة في هذا الطابور';

  @override
  String get selectAll => 'تحديد الكل';

  @override
  String servicesSelected(int count, int total) {
    return '$count من $total محدد';
  }

  @override
  String get queueNameRequired => 'اسم الطابور مطلوب';

  @override
  String get queueNameMinLength => 'يجب أن يكون الاسم على الأقل حرفين';

  @override
  String get queueNameMaxLength => 'لا يمكن أن يتجاوز اسم الطابور 100 حرف';

  @override
  String get loadingServices => 'جاري تحميل الخدمات...';

  @override
  String get credits => 'نقاط';

  @override
  String get pleaseSelectAtLeastOneService =>
      'يرجى اختيار خدمة واحدة على الأقل';

  @override
  String get queueOperatingHours => 'ساعات عمل الطابور';

  @override
  String get setQueueAvailabilityDescription =>
      'حدد متى يكون هذا الطابور متاحًا للمواعيد';

  @override
  String get noServicesAvailable => 'لا توجد خدمات متاحة';

  @override
  String get createServicesFirstMessage =>
      'أنشئ الخدمات أولاً لتعيينها للطوابير.';

  @override
  String get addServices => 'إضافة خدمات';

  @override
  String get serviceDeliveryType => 'نوع تسليم الخدمة';

  @override
  String get atBusinessLocation => 'في موقع العمل';

  @override
  String get atCustomerLocation => 'في موقع العميل';

  @override
  String get bothOptions => 'كلا الموقعين';

  @override
  String get selectDeliveryType => 'يرجى اختيار نوع التسليم';

  @override
  String get selectRegions => 'اختر المناطق...';

  @override
  String regionsSelected(int count) {
    return 'تم اختيار $count منطقة';
  }

  @override
  String get customColor => 'لون مخصص';

  @override
  String get invalidHexColor => 'لون سادس عشري غير صحيح';

  @override
  String editServiceTitle(String serviceName) {
    return 'تعديل $serviceName';
  }

  @override
  String updateServiceDescription(String serviceName) {
    return 'تحديث تفاصيل وإعدادات \"$serviceName\"';
  }

  @override
  String get changesWillBeSaved =>
      'سيتم حفظ التغييرات فوراً وستنعكس في عروض خدماتك. المواعيد الموجودة لن تتأثر.';

  @override
  String errorLoadingService(String error) {
    return 'خطأ في تحميل الخدمة: $error';
  }

  @override
  String errorRefreshingService(String error) {
    return 'خطأ في تحديث الخدمة: $error';
  }

  @override
  String serviceUpdatedSuccessfully(String serviceName) {
    return 'تم تحديث الخدمة \"$serviceName\" بنجاح';
  }

  @override
  String get failedToUpdateService => 'فشل في تحديث الخدمة';

  @override
  String errorUpdatingService(String error) {
    return 'خطأ في تحديث الخدمة: $error';
  }

  @override
  String get price => 'السعر (دج)';

  @override
  String get priceRequired => 'السعر مطلوب';

  @override
  String get enterValidPrice => 'أدخل سعراً صحيحاً';

  @override
  String get serviceDelivery => 'تقديم الخدمة';

  @override
  String get whereProvideService => 'أين تقدم هذه الخدمة؟';

  @override
  String get appearance => 'المظهر';

  @override
  String get serviceOptions => 'خيارات الخدمة';

  @override
  String get publicService => 'خدمة عامة';

  @override
  String get visibleToAllCustomers => 'مرئية لجميع العملاء';

  @override
  String get acceptOnlineBookings => 'قبول الحجوزات عبر الإنترنت';

  @override
  String get allowCustomersBookOnline => 'السماح للعملاء بالحجز عبر الإنترنت';

  @override
  String get acceptNewCustomers => 'قبول عملاء جدد';

  @override
  String get allowNewCustomersBook => 'السماح للعملاء الجدد بالحجز';

  @override
  String get enableNotifications => 'تفعيل الإشعارات';

  @override
  String get getNotifiedNewBookings => 'الحصول على إشعارات للحجوزات الجديدة';

  @override
  String get serviceAvailableForBooking => 'الخدمة متاحة للحجز';

  @override
  String get selectServedRegionsError =>
      'يرجى تحديد المناطق المخدومة لخدمات موقع العميل';

  @override
  String get themeModeLight => 'فاتح';

  @override
  String get themeModeDark => 'داكن';

  @override
  String get themeModeSystem => 'النظام';

  @override
  String get searchServices => 'البحث عن الخدمات...';

  @override
  String get tryAdjustingSearchTerms => 'حاول تعديل مصطلحات البحث';

  @override
  String get noServicesFound => 'لم يتم العثور على خدمات';

  @override
  String get errorLoadingServices => 'خطأ في تحميل الخدمات';

  @override
  String get noLocationsFound => 'لم يتم العثور على مواقع';

  @override
  String get failedToLoadLocations => 'فشل في تحميل المواقع';

  @override
  String get noLocationsAvailable => 'لا توجد مواقع متاحة';

  @override
  String get noCustomersFound => 'لم يتم العثور على عملاء';

  @override
  String get failedToLoadCustomers => 'فشل في تحميل العملاء';

  @override
  String get addFirstCustomerToGetStarted => 'أضف عميلك الأول للبدء';

  @override
  String get addCustomer => 'إضافة عميل';

  @override
  String get noAppointmentsFound => 'لم يتم العثور على مواعيد';

  @override
  String get tryAdjustingYourFilters => 'جرب تعديل المرشحات';

  @override
  String get createFirstAppointmentToGetStarted => 'أنشئ موعدك الأول للبدء';

  @override
  String get addAppointment => 'إضافة موعد';

  @override
  String get noAppointmentsScheduled => 'لا توجد مواعيد مجدولة';

  @override
  String get tapPlusButtonToAddAppointment => 'اضغط على زر + لإضافة موعد';

  @override
  String get tapToChange => 'اضغط للتغيير';

  @override
  String get emailOrPhone => 'البريد الإلكتروني أو الهاتف';

  @override
  String get enterEmailOrPhone => 'أدخل بريدك الإلكتروني أو رقم هاتفك';

  @override
  String get pleaseEnterEmailOrPhone => 'يرجى إدخال بريدك الإلكتروني أو هاتفك';

  @override
  String get pleaseEnterPassword => 'يرجى إدخال كلمة المرور';

  @override
  String get joinDaltiProvider => 'انضم إلى دالتي للمزودين';

  @override
  String get createBusinessAccount => 'أنشئ حساب عملك التجاري';

  @override
  String get selectBusinessCategory => 'اختر فئة عملك التجاري';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get pleaseEnterBusinessName => 'يرجى إدخال اسم عملك التجاري';

  @override
  String get businessCategory => 'فئة العمل التجاري';

  @override
  String get pleaseEnterEmail => 'يرجى إدخال بريدك الإلكتروني';

  @override
  String get pleaseEnterPhoneNumber => 'يرجى إدخال رقم هاتفك';

  @override
  String get pleaseConfirmPassword => 'يرجى تأكيد كلمة المرور';

  @override
  String get resetPasswordDescription =>
      'أدخل عنوان بريدك الإلكتروني وسنرسل لك رمز التحقق لإعادة تعيين كلمة المرور';

  @override
  String get emailAddress => 'عنوان البريد الإلكتروني';

  @override
  String get enterEmailAddress => 'أدخل عنوان بريدك الإلكتروني';

  @override
  String get pleaseEnterEmailAddress => 'يرجى إدخال عنوان بريدك الإلكتروني';

  @override
  String get pleaseEnterValidEmailAddress =>
      'يرجى إدخال عنوان بريد إلكتروني صحيح';

  @override
  String get sendResetCode => 'إرسال رمز إعادة التعيين';

  @override
  String get backToLogin => 'العودة إلى تسجيل الدخول';

  @override
  String get resetCodeSent => 'تم إرسال رمز إعادة التعيين!';

  @override
  String resetCodeSentDescription(String email) {
    return 'لقد أرسلنا رمز التحقق إلى $email';
  }

  @override
  String get continueToVerification => 'المتابعة إلى التحقق';

  @override
  String get resendCode => 'إعادة إرسال الرمز';

  @override
  String resendCodeIn(int seconds) {
    return 'إعادة إرسال الرمز خلال $seconds ثانية';
  }

  @override
  String get verifyResetCode => 'التحقق من رمز إعادة التعيين';

  @override
  String verifyResetCodeDescription(String email) {
    return 'لقد أرسلنا رمزاً مكوناً من 6 أرقام إلى\n$email';
  }

  @override
  String get enterVerificationCode => 'أدخل رمز التحقق';

  @override
  String codeExpiresIn(String minutes, String seconds) {
    return 'ينتهي الرمز خلال $minutes:$seconds';
  }

  @override
  String get codeHasExpired => 'انتهت صلاحية الرمز';

  @override
  String get verifyCode => 'التحقق من الرمز';

  @override
  String get resendCodeWhenExpired => 'إعادة إرسال الرمز عند انتهاء الصلاحية';

  @override
  String get backToEmailEntry => 'العودة إلى إدخال البريد الإلكتروني';

  @override
  String get createNewPassword => 'إنشاء كلمة مرور جديدة';

  @override
  String get createNewPasswordDescription => 'يرجى إنشاء كلمة مرور قوية لحسابك';

  @override
  String resetTokenExpiresIn(int minutes) {
    return 'ينتهي رمز إعادة التعيين خلال $minutes دقيقة';
  }

  @override
  String get resetTokenExpired =>
      'انتهت صلاحية رمز إعادة التعيين. يرجى طلب إعادة تعيين كلمة مرور جديدة.';

  @override
  String get enterNewPassword => 'أدخل كلمة المرور الجديدة';

  @override
  String get confirmNewPasswordHint => 'أكد كلمة المرور الجديدة';

  @override
  String get passwordRequirements => 'متطلبات كلمة المرور';

  @override
  String get atLeast8Characters => '8 أحرف على الأقل';

  @override
  String get containsLowercaseLetter => 'يحتوي على حرف صغير';

  @override
  String get containsUppercaseLetter => 'يحتوي على حرف كبير';

  @override
  String get containsNumber => 'يحتوي على رقم';

  @override
  String get containsSpecialCharacter => 'يحتوي على رمز خاص';

  @override
  String get resetPasswordButton => 'إعادة تعيين كلمة المرور';

  @override
  String get passwordDoesNotMeetRequirements => 'كلمة المرور لا تلبي المتطلبات';

  @override
  String get createFirstService => 'أنشئ خدمتك الأولى للبدء';

  @override
  String get addService => 'إضافة خدمة';

  @override
  String get searchLocations => 'البحث عن المواقع...';

  @override
  String get createFirstLocation => 'أنشئ موقعك الأول للبدء';

  @override
  String get addLocation => 'إضافة موقع';

  @override
  String get schedulingInformation => 'معلومات الجدولة';

  @override
  String get date => 'التاريخ';

  @override
  String get unknown => 'غير معروف';

  @override
  String get businessSetup => 'إعداد العمل';

  @override
  String get skipSetupTooltip => 'Skip setup';

  @override
  String get businessInformation => 'معلومات العمل';

  @override
  String get tellUsAboutYourBusiness => 'أخبرنا عن عملك';

  @override
  String get businessNameRequired => 'اسم العمل مطلوب';

  @override
  String get businessNameMinLength => 'يجب أن يكون اسم العمل على الأقل حرفين';

  @override
  String get enterYourBusinessName => 'أدخل اسم عملك';

  @override
  String get businessDescription => 'وصف العمل';

  @override
  String get businessDescriptionRequired => 'وصف العمل مطلوب';

  @override
  String get businessDescriptionMinLength =>
      'يجب أن يحتوي الوصف على 10 أحرف على الأقل';

  @override
  String get describeWhatYourBusinessDoes => 'صف ما يقوم به عملك';

  @override
  String get businessCategoryRequired => 'يرجى اختيار فئة العمل';

  @override
  String get shortNameOptional => 'الاسم المختصر (اختياري)';

  @override
  String get businessLogoOptional => 'شعار العمل (اختياري)';

  @override
  String get uploadBusinessLogoDescription =>
      'ارفع شعار عملك لمساعدة العملاء على التعرف على علامتك التجارية';

  @override
  String get clickToUploadLogo => 'انقر لرفع الشعار';

  @override
  String get businessInfoDescription =>
      'This information will be displayed to your customers and used to set up your business profile.';

  @override
  String get onboardingWelcomeTitle => 'مرحباً بك في دالتي للمقدمين!';

  @override
  String get onboardingWelcomeSubtitle =>
      'دعنا نقوم بإعداد ملف تعريف عملك ونجهزك لخدمة العملاء.';

  @override
  String get onboardingSetup => 'الإعداد';

  @override
  String get onboardingSkipForNow => 'تخطي الآن';

  @override
  String get onboardingTimeEstimate => 'يستغرق حوالي 5-10 دقائق للإكمال';

  @override
  String get onboardingSetupSteps => 'خطوات الإعداد';

  @override
  String get onboardingStepBusinessInfo => 'معلومات العمل';

  @override
  String get onboardingStepBusinessInfoDesc => 'أخبرنا عن عملك وتفاصيل الاتصال';

  @override
  String get onboardingStepLocationSetup => 'إعداد الموقع';

  @override
  String get onboardingStepLocationSetupDesc => 'أضف مواقع عملك وساعات العمل';

  @override
  String get onboardingStepServiceCreation => 'إنشاء الخدمات';

  @override
  String get onboardingStepServiceCreationDesc =>
      'حدد الخدمات التي تقدمها للعملاء';

  @override
  String get onboardingStepQueueManagement => 'إدارة الطوابير';

  @override
  String get onboardingStepQueueManagementDesc =>
      'قم بإعداد الطوابير لتنظيم خدماتك';

  @override
  String get onboardingWhyUseDalti => 'لماذا تستخدم دالتي للمقدمين؟';

  @override
  String get onboardingBenefitManageCustomers => 'إدارة العملاء';

  @override
  String get onboardingBenefitManageCustomersDesc =>
      'نظم وتتبع مواعيد عملائك بكفاءة';

  @override
  String get onboardingBenefitSmartScheduling => 'الجدولة الذكية';

  @override
  String get onboardingBenefitSmartSchedulingDesc =>
      'إدارة الطوابير الآلية وجدولة المواعيد';

  @override
  String get onboardingBenefitBusinessInsights => 'رؤى العمل';

  @override
  String get onboardingBenefitBusinessInsightsDesc =>
      'تتبع الأداء وانمِ عملك بالتحليلات';

  @override
  String get onboardingBenefitMobileReady => 'جاهز للهاتف المحمول';

  @override
  String get onboardingBenefitMobileReadyDesc =>
      'اوصل إلى لوحة تحكم عملك في أي مكان وأي وقت';

  @override
  String get onboardingStepWelcome => 'مرحباً';

  @override
  String get onboardingStepWelcomeDesc => 'مرحباً بك في دالتي للمقدمين';

  @override
  String get onboardingStepBusinessProfile => 'معلومات العمل';

  @override
  String get onboardingStepBusinessProfileDesc => 'أخبرنا عن عملك';

  @override
  String get onboardingStepLocation => 'الموقع';

  @override
  String get onboardingStepLocationDesc => 'أضف موقع عملك';

  @override
  String get onboardingStepServices => 'الخدمات';

  @override
  String get onboardingStepServicesDesc => 'حدد خدماتك';

  @override
  String get onboardingStepQueues => 'الطوابير';

  @override
  String get onboardingStepQueuesDesc => 'اكوّن طوابيرك';

  @override
  String get onboardingStepSummary => 'الملخص';

  @override
  String get onboardingStepSummaryDesc => 'راجع إعدادك';

  @override
  String get onboardingStepCompleted => 'مكتمل';

  @override
  String get onboardingStepCompletedDesc => 'الإعداد مكتمل!';

  @override
  String get onboardingPrimaryLocation => 'الموقع الرئيسي';

  @override
  String get otpVerificationSuccessTitle => 'تم التحقق من رمز OTP بنجاح!';

  @override
  String get otpVerificationSuccessMessage =>
      'تم التحقق من هويتك. يمكنك الآن المتابعة لإعادة تعيين كلمة المرور.';

  @override
  String get continueToResetPassword => 'متابعة';

  @override
  String get welcomeToApp => 'مرحباً بك في';

  @override
  String get welcomeSubtitle => 'حلك الشامل لإدارة الأعمال';

  @override
  String get welcomeDescription =>
      'قم بتبسيط عمليات عملك مع منصتنا الشاملة المصممة لمقدمي الخدمات. أدر المواعيد والطوابير والعملاء وانمِ عملك بكفاءة.';

  @override
  String get getStartedNow => 'ابدأ الآن';

  @override
  String get signInHere => 'تسجيل الدخول';

  @override
  String get welcomeFeatureManageAppointments => 'إدارة المواعيد';

  @override
  String get welcomeFeatureManageAppointmentsDesc =>
      'جدول وتتبع مواعيد العملاء بسهولة';

  @override
  String get welcomeFeatureQueueManagement => 'إدارة الطوابير';

  @override
  String get welcomeFeatureQueueManagementDesc =>
      'نظم الخدمات وقلل أوقات انتظار العملاء';

  @override
  String get welcomeFeatureBusinessInsights => 'تحليلات الأعمال';

  @override
  String get welcomeFeatureBusinessInsightsDesc =>
      'تتبع الأداء واتخذ قرارات مبنية على البيانات';

  @override
  String get switchToLightMode => 'التبديل إلى الوضع الفاتح';

  @override
  String get switchToDarkMode => 'التبديل إلى الوضع الداكن';

  @override
  String get termsAndPrivacyText =>
      'بالمتابعة، فإنك توافق على شروط الخدمة وسياسة الخصوصية الخاصة بنا.';

  @override
  String get welcomeTitle => 'مرحباً بك في دالتي';

  @override
  String get welcomeMessage =>
      'أدر جدولك الزمني وعملائك بسهولة. تواصل مع العملاء واحجز المواعيد.';

  @override
  String get logIn => 'تسجيل الدخول';

  @override
  String get completingSetup => 'جاري إكمال الإعداد...';

  @override
  String get businessProfileInfoCard =>
      'ستظهر هذه المعلومات لعملائك وستُستخدم لإعداد ملف عملك التجاري.';

  @override
  String yourServicesCount(int count) {
    return 'خدماتك ($count)';
  }

  @override
  String get addYourFirstService => 'أضف خدمتك الأولى';

  @override
  String get addAnotherService => 'أضف خدمة أخرى';

  @override
  String get addServiceInfoMessage =>
      'أضف خدمة واحدة على الأقل للمتابعة. يمكنك إضافة المزيد من الخدمات لاحقاً من قسم إدارة الخدمات.';

  @override
  String get online => 'عبر الإنترنت';

  @override
  String get newClients => 'عملاء جدد';

  @override
  String get addNewService => 'إضافة خدمة جديدة';

  @override
  String get serviceTitleMinLength => 'يجب أن يكون العنوان على الأقل حرفين';

  @override
  String get serviceDescription => 'الوصف';

  @override
  String get serviceDescriptionHint => 'اوصف خدمتك';

  @override
  String get durationMinutes => 'المدة (بالدقائق)';

  @override
  String get priceDA => 'السعر (دج)';

  @override
  String get allowCustomersToBookOnline => 'السماح للعملاء بالحجز عبر الإنترنت';

  @override
  String get allowNewCustomersToBook => 'السماح للعملاء الجدد بالحجز';

  @override
  String get getNotifiedForNewBookings => 'احصل على إشعارات للحجوزات الجديدة';

  @override
  String get creditPointsPositiveError =>
      'يجب أن تكون نقاط الائتمان رقماً موجباً';

  @override
  String get bothLocations => 'كلا الموقعين';

  @override
  String get pleaseSelectDeliveryType => 'يرجى اختيار نوع التسليم';

  @override
  String get servedRegions => 'المناطق المخدومة';

  @override
  String get chooseWilayasHint => 'اختر الولايات التي تقدم فيها الخدمات';

  @override
  String get searchWilayasHint => 'البحث في الولايات...';

  @override
  String get selectAtLeastOneWilaya =>
      'يرجى اختيار ولاية واحدة على الأقل لخدمات موقع العميل';

  @override
  String serviceAddedSuccessfully(String serviceName) {
    return 'تم إضافة الخدمة \"$serviceName\" بنجاح!';
  }

  @override
  String get removeService => 'إزالة الخدمة';

  @override
  String removeServiceConfirmation(String serviceName) {
    return 'هل أنت متأكد من أنك تريد إزالة \"$serviceName\"؟';
  }

  @override
  String get uploadYourBusinessLogo =>
      'ارفع شعار عملك لمساعدة العملاء على التعرف على علامتك التجارية';

  @override
  String stepCounter(int current, int total) {
    return '$current/$total';
  }

  @override
  String get chooseImageFromDevice => 'اختر ملف صورة من جهازك';

  @override
  String get supportedFormatsInfo =>
      'الصيغ المدعومة: PNG, JPG, JPEG • الحد الأقصى: 5MB';

  @override
  String get uploadingLogo => 'جاري رفع الشعار...';

  @override
  String get logoUploadedSuccessfully => 'تم رفع الشعار بنجاح!';

  @override
  String get logoRemovedSuccessfully => 'تم حذف الشعار بنجاح!';

  @override
  String get failedToReadFileData => 'فشل في قراءة بيانات الملف';

  @override
  String get fileSizeMustBeLessThan5MB => 'يجب أن يكون حجم الملف أقل من 5MB';

  @override
  String get atLeastOnePhoneRequired =>
      'مطلوب رقم هاتف واحد على الأقل للتواصل مع العملاء.';

  @override
  String get mobilePhone => 'الهاتف المحمول';

  @override
  String get mobilePhoneRecommended => 'الهاتف المحمول (مُوصى به)';

  @override
  String get landlinePhone => 'الهاتف الثابت';

  @override
  String get faxNumber => 'رقم الفاكس';

  @override
  String get mobilePhoneHint => 'مثال: +213 555 123 456';

  @override
  String get landlinePhoneHint => 'مثال: +213 21 123 456';

  @override
  String get faxNumberHint => 'مثال: +213 21 123 457';

  @override
  String get atLeastOnePhoneRequiredValidation =>
      'مطلوب رقم هاتف واحد على الأقل';

  @override
  String get currentLocation => 'الموقع الحالي';

  @override
  String get setBusinessOperatingHours =>
      'حدد ساعات عمل نشاطك التجاري لكل يوم من أيام الأسبوع';

  @override
  String get primaryLocationDescription =>
      'سيكون هذا موقع عملك الأساسي. يمكنك إضافة مواقع أخرى لاحقاً من قسم إدارة المواقع';

  @override
  String get currentlyOnlyAvailableInAlgeria => 'متاح حالياً في الجزائر فقط';

  @override
  String get addHours => 'إضافة ساعات';

  @override
  String get enterCompleteAddress => 'يرجى إدخال عنوان كامل';

  @override
  String get creditPointsRequiredValidation => 'متطلبات نقاط الائتمان مطلوبة';

  @override
  String get creditPointsPositiveNumber =>
      'يجب أن تكون نقاط الائتمان رقماً موجباً';

  @override
  String get createYourFirstQueue => 'أنشئ طابورك الأول';

  @override
  String get addAnotherQueue => 'إضافة طابور آخر';

  @override
  String get createQueueDescription =>
      'أنشئ طابوراً واحداً على الأقل لتنظيم خدماتك. يمكن لكل طابور أن يكون له ساعات عمل وخدمات مخصصة';

  @override
  String get createNewQueue => 'إنشاء طابور جديد';

  @override
  String get assignServices => 'تعيين الخدمات';

  @override
  String get selectServicesForQueue =>
      'اختر الخدمات التي ستكون متاحة في هذا الطابور';

  @override
  String get timeSlots => 'الفترات الزمنية';

  @override
  String get reviewYourSetup => 'مراجعة الإعداد';

  @override
  String get reviewBusinessInformation =>
      'يرجى مراجعة معلومات نشاطك التجاري قبل إكمال الإعداد';

  @override
  String get businessProfile => 'الملف التجاري';

  @override
  String get completeSetup => 'إكمال الإعداد';

  @override
  String get nameLabel => 'الاسم';

  @override
  String get categoryLabel => 'الفئة';

  @override
  String get descriptionLabel => 'الوصف';

  @override
  String get mobileLabel => 'الجوال';

  @override
  String get cityLabel => 'المدينة';

  @override
  String get addressLabel => 'العنوان';

  @override
  String get servicesConfigured => 'خدمة مُعدة';

  @override
  String get readyToCompleteSetup => 'جاهز لإكمال الإعداد';

  @override
  String get businessReadyMessage =>
      'نشاطك التجاري جاهز للانطلاق! لقد قمت بإعداد ملفك الشخصي والموقع والخدمات والطوابير مع ساعات العمل. انقر على \"إكمال الإعداد\" للانتهاء.';

  @override
  String get queuesConfiguredWithHours => 'طابور مُعد مع ساعات العمل';

  @override
  String get dashboardShort => 'الرئيسية';

  @override
  String get calendarShort => 'التقويم';

  @override
  String get customersShort => 'العملاء';

  @override
  String get appointmentsShort => 'المواعيد';
}
