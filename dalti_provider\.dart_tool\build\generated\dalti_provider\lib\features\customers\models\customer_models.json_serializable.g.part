// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomerImpl _$$CustomerImplFromJson(Map<String, dynamic> json) =>
    _$CustomerImpl(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String?,
      phoneNumber: json['mobileNumber'] as String?,
      nationalId: json['nationalId'] as String?,
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      address: json['address'] as String?,
      city: json['city'] as String?,
      wilaya: json['wilaya'] as String?,
      notes: json['notes'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      totalAppointments: (json['appointmentCount'] as num?)?.toInt() ?? 0,
      totalSpent: (json['totalSpent'] as num?)?.toDouble() ?? 0.0,
      lastAppointmentDate: json['lastAppointmentDate'] == null
          ? null
          : DateTime.parse(json['lastAppointmentDate'] as String),
      status: $enumDecodeNullable(_$CustomerStatusEnumMap, json['status']) ??
          CustomerStatus.active,
      preferences: json['preferences'] == null
          ? null
          : CustomerPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerImplToJson(_$CustomerImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'firstName': instance.firstName,
    'lastName': instance.lastName,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('email', instance.email);
  writeNotNull('mobileNumber', instance.phoneNumber);
  writeNotNull('nationalId', instance.nationalId);
  writeNotNull('dateOfBirth', instance.dateOfBirth?.toIso8601String());
  writeNotNull('address', instance.address);
  writeNotNull('city', instance.city);
  writeNotNull('wilaya', instance.wilaya);
  writeNotNull('notes', instance.notes);
  val['tags'] = instance.tags;
  val['createdAt'] = instance.createdAt.toIso8601String();
  writeNotNull('updatedAt', instance.updatedAt?.toIso8601String());
  val['appointmentCount'] = instance.totalAppointments;
  val['totalSpent'] = instance.totalSpent;
  writeNotNull(
      'lastAppointmentDate', instance.lastAppointmentDate?.toIso8601String());
  val['status'] = _$CustomerStatusEnumMap[instance.status]!;
  writeNotNull('preferences', instance.preferences?.toJson());
  return val;
}

const _$CustomerStatusEnumMap = {
  CustomerStatus.active: 'active',
  CustomerStatus.inactive: 'inactive',
  CustomerStatus.blocked: 'blocked',
};

_$CustomerPreferencesImpl _$$CustomerPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerPreferencesImpl(
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      smsNotifications: json['smsNotifications'] as bool? ?? true,
      appointmentReminders: json['appointmentReminders'] as bool? ?? true,
      promotionalMessages: json['promotionalMessages'] as bool? ?? true,
      preferredLanguage: json['preferredLanguage'] as String?,
      preferredContactMethod: json['preferredContactMethod'] as String?,
    );

Map<String, dynamic> _$$CustomerPreferencesImplToJson(
    _$CustomerPreferencesImpl instance) {
  final val = <String, dynamic>{
    'emailNotifications': instance.emailNotifications,
    'smsNotifications': instance.smsNotifications,
    'appointmentReminders': instance.appointmentReminders,
    'promotionalMessages': instance.promotionalMessages,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('preferredLanguage', instance.preferredLanguage);
  writeNotNull('preferredContactMethod', instance.preferredContactMethod);
  return val;
}

_$CustomerAppointmentImpl _$$CustomerAppointmentImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerAppointmentImpl(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      serviceId: json['serviceId'] as String,
      serviceName: json['serviceName'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      status: $enumDecode(_$AppointmentStatusEnumMap, json['status']),
      locationId: json['locationId'] as String?,
      locationName: json['locationName'] as String?,
      providerId: json['providerId'] as String?,
      providerName: json['providerName'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      duration: (json['duration'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$CustomerAppointmentImplToJson(
    _$CustomerAppointmentImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'customerId': instance.customerId,
    'serviceId': instance.serviceId,
    'serviceName': instance.serviceName,
    'scheduledTime': instance.scheduledTime.toIso8601String(),
    'status': _$AppointmentStatusEnumMap[instance.status]!,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('locationId', instance.locationId);
  writeNotNull('locationName', instance.locationName);
  writeNotNull('providerId', instance.providerId);
  writeNotNull('providerName', instance.providerName);
  writeNotNull('price', instance.price);
  writeNotNull('duration', instance.duration);
  writeNotNull('notes', instance.notes);
  writeNotNull('createdAt', instance.createdAt?.toIso8601String());
  writeNotNull('updatedAt', instance.updatedAt?.toIso8601String());
  return val;
}

const _$AppointmentStatusEnumMap = {
  AppointmentStatus.scheduled: 'scheduled',
  AppointmentStatus.confirmed: 'confirmed',
  AppointmentStatus.inProgress: 'in_progress',
  AppointmentStatus.completed: 'completed',
  AppointmentStatus.canceled: 'canceled',
  AppointmentStatus.noShow: 'no_show',
};

_$CustomerSearchFiltersImpl _$$CustomerSearchFiltersImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerSearchFiltersImpl(
      searchQuery: json['searchQuery'] as String?,
      status: $enumDecodeNullable(_$CustomerStatusEnumMap, json['status']),
      wilaya: json['wilaya'] as String?,
      createdAfter: json['createdAfter'] == null
          ? null
          : DateTime.parse(json['createdAfter'] as String),
      createdBefore: json['createdBefore'] == null
          ? null
          : DateTime.parse(json['createdBefore'] as String),
      minAppointments: (json['minAppointments'] as num?)?.toInt() ?? 0,
      minSpent: (json['minSpent'] as num?)?.toDouble() ?? 0.0,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      sortBy: json['sortBy'] as String? ?? 'firstName',
      ascending: json['ascending'] as bool? ?? true,
    );

Map<String, dynamic> _$$CustomerSearchFiltersImplToJson(
    _$CustomerSearchFiltersImpl instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('searchQuery', instance.searchQuery);
  writeNotNull('status', _$CustomerStatusEnumMap[instance.status]);
  writeNotNull('wilaya', instance.wilaya);
  writeNotNull('createdAfter', instance.createdAfter?.toIso8601String());
  writeNotNull('createdBefore', instance.createdBefore?.toIso8601String());
  val['minAppointments'] = instance.minAppointments;
  val['minSpent'] = instance.minSpent;
  writeNotNull('tags', instance.tags);
  val['sortBy'] = instance.sortBy;
  val['ascending'] = instance.ascending;
  return val;
}

_$CustomersResponseImpl _$$CustomersResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomersResponseImpl(
      success: json['success'] as bool,
      customers: (json['customers'] as List<dynamic>?)
              ?.map((e) => Customer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 20,
      totalPages: (json['totalPages'] as num?)?.toInt() ?? 0,
      error: json['error'] == null
          ? null
          : CustomerError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomersResponseImplToJson(
    _$CustomersResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
    'customers': instance.customers.map((e) => e.toJson()).toList(),
    'totalCount': instance.totalCount,
    'currentPage': instance.currentPage,
    'pageSize': instance.pageSize,
    'totalPages': instance.totalPages,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$CustomerResponseImpl _$$CustomerResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerResponseImpl(
      success: json['success'] as bool,
      customer: json['customer'] == null
          ? null
          : Customer.fromJson(json['customer'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : CustomerError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerResponseImplToJson(
    _$CustomerResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('customer', instance.customer?.toJson());
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$CustomerAppointmentsResponseImpl _$$CustomerAppointmentsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerAppointmentsResponseImpl(
      success: json['success'] as bool,
      appointments: (json['appointments'] as List<dynamic>?)
              ?.map((e) =>
                  CustomerAppointment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      error: json['error'] == null
          ? null
          : CustomerError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerAppointmentsResponseImplToJson(
    _$CustomerAppointmentsResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
    'appointments': instance.appointments.map((e) => e.toJson()).toList(),
    'totalCount': instance.totalCount,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$CustomerErrorImpl _$$CustomerErrorImplFromJson(Map<String, dynamic> json) =>
    _$CustomerErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
    );

Map<String, dynamic> _$$CustomerErrorImplToJson(_$CustomerErrorImpl instance) {
  final val = <String, dynamic>{
    'code': instance.code,
    'message': instance.message,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('details', instance.details);
  return val;
}

_$CustomerRequestImpl _$$CustomerRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerRequestImpl(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      mobileNumber: json['mobileNumber'] as String?,
      email: json['email'] as String?,
      nationalId: json['nationalId'] as String?,
      notes: json['notes'] as String?,
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      address: json['address'] as String?,
      city: json['city'] as String?,
      wilaya: json['wilaya'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      preferences: json['preferences'] == null
          ? null
          : CustomerPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerRequestImplToJson(
    _$CustomerRequestImpl instance) {
  final val = <String, dynamic>{
    'firstName': instance.firstName,
    'lastName': instance.lastName,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('mobileNumber', instance.mobileNumber);
  writeNotNull('email', instance.email);
  writeNotNull('nationalId', instance.nationalId);
  writeNotNull('notes', instance.notes);
  writeNotNull('dateOfBirth', instance.dateOfBirth?.toIso8601String());
  writeNotNull('address', instance.address);
  writeNotNull('city', instance.city);
  writeNotNull('wilaya', instance.wilaya);
  val['tags'] = instance.tags;
  writeNotNull('preferences', instance.preferences?.toJson());
  return val;
}

_$CustomerStatsImpl _$$CustomerStatsImplFromJson(Map<String, dynamic> json) =>
    _$CustomerStatsImpl(
      totalCustomers: (json['totalCustomers'] as num?)?.toInt() ?? 0,
      newCustomersThisMonth:
          (json['newCustomersThisMonth'] as num?)?.toInt() ?? 0,
      activeCustomers: (json['activeCustomers'] as num?)?.toInt() ?? 0,
      averageSpentPerCustomer:
          (json['averageSpentPerCustomer'] as num?)?.toDouble() ?? 0.0,
      customerRetentionRate:
          (json['customerRetentionRate'] as num?)?.toDouble() ?? 0.0,
      customersByWilaya: (json['customersByWilaya'] as List<dynamic>?)
              ?.map(
                  (e) => CustomersByWilaya.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CustomerStatsImplToJson(_$CustomerStatsImpl instance) =>
    <String, dynamic>{
      'totalCustomers': instance.totalCustomers,
      'newCustomersThisMonth': instance.newCustomersThisMonth,
      'activeCustomers': instance.activeCustomers,
      'averageSpentPerCustomer': instance.averageSpentPerCustomer,
      'customerRetentionRate': instance.customerRetentionRate,
      'customersByWilaya':
          instance.customersByWilaya.map((e) => e.toJson()).toList(),
    };

_$CustomersByWilayaImpl _$$CustomersByWilayaImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomersByWilayaImpl(
      wilaya: json['wilaya'] as String,
      count: (json['count'] as num).toInt(),
      percentage: (json['percentage'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$CustomersByWilayaImplToJson(
        _$CustomersByWilayaImpl instance) =>
    <String, dynamic>{
      'wilaya': instance.wilaya,
      'count': instance.count,
      'percentage': instance.percentage,
    };
