# Web Notifications Setup Guide

## Current Status
✅ **Android notifications**: Working perfectly  
✅ **iOS notifications**: Configured and ready (needs testing on Mac)  
⚠️ **Web notifications**: Needs VAPID key configuration

## Quick Fix for Web Notifications

### Step 1: Get VAPID Key from Firebase Console

1. **Open Firebase Console**: Go to [https://console.firebase.google.com](https://console.firebase.google.com)
2. **Select Project**: Choose "dalti-prod" project
3. **Navigate to Settings**: Click the gear icon ⚙️ next to "Project Overview"
4. **Go to Cloud Messaging**: Click on the "Cloud Messaging" tab
5. **Find Web Push Certificates**: Scroll down to "Web configuration" section
6. **Copy VAPID Key**: Look for "Web Push certificates" and copy the key (starts with 'B' and is ~88 characters long)

### Step 2: Update the Code

Replace the placeholder in `lib/core/services/firebase_messaging_service.dart`:

```dart
// Find this line (around line 429):
const vapidKey = 'PLACEHOLDER_VAPID_KEY'; // Replace with actual key

// Replace with your actual VAPID key:
const vapidKey = 'BKagOny0KF_2pCJQ3mFfQRLQjdX8C9H5b2X1vZ9Y8W7V6U5T4S3R2Q1P0O9N8M7L6K5J4I3H2G1F0E9D8C7B6A5'; // Example format
```

### Step 3: Test Web Notifications

1. **Run on Chrome**: `flutter run -d chrome`
2. **Check Console**: Look for FCM logs in browser developer tools
3. **Test Notifications**: Use the notification test screen in your app

## Alternative: Disable Web Notifications

If you prefer to skip web notifications entirely (since web is only for UI testing):

```dart
// Replace the web block with:
if (kIsWeb) {
  print('[FCM] Skipping web notifications - web is for UI testing only');
  return;
}
```

## Technical Details

- **VAPID Key**: Required for web push notifications security
- **Service Worker**: Already configured in `web/firebase-messaging-sw.js`
- **Manifest**: Already configured in `web/manifest.json`
- **No Impact**: This change only affects web, Android/iOS remain unchanged

## Current Configuration Status

| Platform | Status | Details |
|----------|---------|---------|
| **Android** | ✅ Working | Firebase configured correctly |
| **iOS** | ✅ Ready | Needs testing on Mac with Xcode |
| **Web** | ⚠️ Needs VAPID | Follow steps above to fix |

## Notes

- Web notifications are not critical since web is only used for UI testing
- Android notifications are working perfectly and won't be affected
- iOS notifications are properly configured and ready for testing
