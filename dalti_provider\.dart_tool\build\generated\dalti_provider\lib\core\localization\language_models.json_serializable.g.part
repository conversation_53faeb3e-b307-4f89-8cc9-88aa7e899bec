// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UpdateLanguageRequestImpl _$$UpdateLanguageRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateLanguageRequestImpl(
      preferedLanguage: json['preferedLanguage'] as String,
    );

Map<String, dynamic> _$$UpdateLanguageRequestImplToJson(
        _$UpdateLanguageRequestImpl instance) =>
    <String, dynamic>{
      'preferedLanguage': instance.preferedLanguage,
    };

_$UpdateLanguageResponseImpl _$$UpdateLanguageResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateLanguageResponseImpl(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$UpdateLanguageResponseImplToJson(
    _$UpdateLanguageResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('message', instance.message);
  writeNotNull('data', instance.data);
  return val;
}

_$LanguageStateImpl _$$LanguageStateImplFromJson(Map<String, dynamic> json) =>
    _$LanguageStateImpl(
      currentLanguage:
          $enumDecode(_$SupportedLanguageEnumMap, json['currentLanguage']),
      isLoading: json['isLoading'] as bool? ?? false,
      isChangingLanguage: json['isChangingLanguage'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$LanguageStateImplToJson(_$LanguageStateImpl instance) {
  final val = <String, dynamic>{
    'currentLanguage': _$SupportedLanguageEnumMap[instance.currentLanguage]!,
    'isLoading': instance.isLoading,
    'isChangingLanguage': instance.isChangingLanguage,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('error', instance.error);
  return val;
}

const _$SupportedLanguageEnumMap = {
  SupportedLanguage.english: 'EN',
  SupportedLanguage.french: 'FR',
  SupportedLanguage.arabic: 'AR',
};
