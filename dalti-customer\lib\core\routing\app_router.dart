import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'app_routes.dart';
import '../../features/auth/screens/splash_screen.dart';
import '../../features/auth/screens/welcome_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/signup_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/verify_email_otp_screen.dart';
import '../../features/auth/screens/verify_reset_otp_screen.dart';
import '../../features/auth/screens/reset_password_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../shared/screens/placeholder_screen.dart';
import '../widgets/main_navigation.dart';

/// App router provider for Dalti Customer app
/// Manages all navigation and routing logic
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    routes: [
      // Splash route
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Welcome route
      GoRoute(
        path: AppRoutes.welcome,
        name: 'welcome',
        builder: (context, state) => const WelcomeScreen(),
      ),

      // Authentication routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const SignupScreen(),
      ),

      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgotPassword',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      GoRoute(
        path: AppRoutes.otpVerification,
        name: 'otpVerification',
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          return VerifyEmailOtpScreen(email: email);
        },
      ),

      GoRoute(
        path: AppRoutes.verifyResetOtp,
        name: 'verifyResetOtp',
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          return VerifyResetOtpScreen(email: email);
        },
      ),

      GoRoute(
        path: AppRoutes.resetPassword,
        name: 'resetPassword',
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          final resetToken = state.uri.queryParameters['resetToken'] ?? '';
          return ResetPasswordScreen(email: email, resetToken: resetToken);
        },
      ),

      // Main app shell with bottom navigation
      ShellRoute(
        builder: (context, state, child) {
          return MainNavigation(currentRoute: state.uri.path, child: child);
        },
        routes: [
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: AppRoutes.search,
            name: 'search',
            builder:
                (context, state) => const PlaceholderScreen(
                  title: 'Search',
                  description: 'Find service providers in your area.',
                  icon: Icons.search,
                ),
          ),
          GoRoute(
            path: AppRoutes.bookings,
            name: 'bookings',
            builder:
                (context, state) => const PlaceholderScreen(
                  title: 'My Bookings',
                  description: 'View and manage your appointments.',
                  icon: Icons.calendar_today,
                ),
          ),
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder:
                (context, state) => const PlaceholderScreen(
                  title: 'Profile',
                  description: 'Manage your account and preferences.',
                  icon: Icons.person,
                ),
          ),
        ],
      ),

      // Provider routes (placeholder)
      GoRoute(
        path: AppRoutes.providerDetails,
        name: 'providerDetails',
        builder: (context, state) {
          final providerId = state.uri.queryParameters['id'] ?? '';
          return PlaceholderScreen(
            title: 'Provider Details',
            description:
                'Provider information and services.\nProvider ID: $providerId',
            icon: Icons.business,
          );
        },
      ),

      // Booking routes (placeholder)
      GoRoute(
        path: AppRoutes.bookAppointment,
        name: 'bookAppointment',
        builder: (context, state) {
          final providerId = state.uri.queryParameters['providerId'] ?? '';
          return PlaceholderScreen(
            title: 'Book Appointment',
            description:
                'Book an appointment with your selected provider.\nProvider ID: $providerId',
            icon: Icons.event_available,
          );
        },
      ),

      // Profile routes (placeholder)
      GoRoute(
        path: AppRoutes.editProfile,
        name: 'editProfile',
        builder:
            (context, state) => const PlaceholderScreen(
              title: 'Edit Profile',
              description: 'Update your personal information.',
              icon: Icons.edit,
            ),
      ),

      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder:
            (context, state) => const PlaceholderScreen(
              title: 'Settings',
              description: 'App settings and preferences.',
              icon: Icons.settings,
            ),
      ),

      // Support routes (placeholder)
      GoRoute(
        path: AppRoutes.help,
        name: 'help',
        builder:
            (context, state) => const PlaceholderScreen(
              title: 'Help',
              description: 'Get help and support.',
              icon: Icons.help,
            ),
      ),

      // Error routes
      GoRoute(
        path: AppRoutes.notFound,
        name: 'notFound',
        builder:
            (context, state) => const PlaceholderScreen(
              title: 'Page Not Found',
              description: 'The page you are looking for does not exist.',
              icon: Icons.error_outline,
            ),
      ),
    ],

    // Error handling
    errorBuilder:
        (context, state) => PlaceholderScreen(
          title: 'Error',
          description: 'An error occurred: ${state.error}',
          icon: Icons.error,
        ),

    // Redirect logic (for authentication, etc.)
    redirect: (context, state) {
      // TODO: Add authentication redirect logic here
      // For now, allow all routes
      return null;
    },
  );
});

/// Navigation helper methods
class AppNavigation {
  static void goToHome(BuildContext context) {
    context.go(AppRoutes.home);
  }

  static void goToLogin(BuildContext context) {
    context.go(AppRoutes.login);
  }

  static void goToSignup(BuildContext context) {
    context.go(AppRoutes.register);
  }

  static void goToProviderDetails(BuildContext context, String providerId) {
    context.go('${AppRoutes.providerDetails}?id=$providerId');
  }

  static void goToBookAppointment(BuildContext context, String providerId) {
    context.go('${AppRoutes.bookAppointment}?providerId=$providerId');
  }

  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      context.go(AppRoutes.home);
    }
  }
}
