"-Xallow-no-source-files" "-classpath" "D:\\dalti-provider-flutter\\dalti_provider\\build\\mobile_scanner\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77a57776ade161208265d2b8c7b03d2c\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b28c8b8fac92fbf6bd61947aa371800d\\transformed\\jetified-camera-core-1.4.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b717d7d01f296afe6f8c28a520f7fff5\\transformed\\jetified-camera-camera2-1.4.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\507ba1ddeeb7798710ea82d21520531a\\transformed\\jetified-camera-lifecycle-1.4.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0296425d8052f36cd50a55715a3b7a99\\transformed\\jetified-barcode-scanning-17.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6ee1448527f2997e08258cb319855e29\\transformed\\jetified-play-services-mlkit-barcode-scanning-18.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\edbe09215a8624f9daa2d2330339149f\\transformed\\jetified-barcode-scanning-common-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c40050bf257fd73b6324cf2226a00a49\\transformed\\jetified-vision-common-17.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bc5b9b69a666c09e9ccecf5c37552443\\transformed\\jetified-common-18.11.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\04c73b9958d21aad09501aa29069639a\\transformed\\jetified-play-services-base-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de1376d11c76f62cbdba5e9ac5696056\\transformed\\jetified-vision-interfaces-16.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9ff767f116fa5fe174aafb21c0131fef\\transformed\\jetified-play-services-tasks-18.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c7a7854de067b0b8b79b93f46d4db1f4\\transformed\\jetified-play-services-basement-18.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f5a864c30cbd8e9b9db90fe3ecdf3a95\\transformed\\appcompat-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d2fc16653ff98449e5afda5923b5294\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ecbcc9dbce6fb0fe70316be126fefcdc\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4e8891bc8975cd0da761148d12055588\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\122a5969b1985d7be42b02b84febb01c\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\264f23c0bbd9a358e77e0d77bfb90a44\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c54b6d82d1b065ea68e10bc84323ab31\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3e246e8ad82edd48d2190c3b67b7dfb6\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a856922fde46c1b802a84bd265d26d70\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7354b1e4fbd90b40efc379347b5df712\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\05891960b183a9002606bccd729cfe8b\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11e992f10fd35a1ac4e9a3d961c3c409\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2ebdd5993b75eb6e23e26ce2965fb23\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\497559898d8250e0f9ea143fae805432\\transformed\\jetified-appcompat-resources-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e01b269438fbba87fa9d47dfb65db6c\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fe5bb3c9738e776debf8642115f99e9\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fe592f154d8c37db6fad3a97d539d9a9\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd4ef2dbd3b3751aeb17cc405443b49f\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b79a76a33a4a075e3fbc3c7022e528ad\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1f60f2a559aa68f208bd72232f85694\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e59fefeb2ef861f1050d323105c3f7d2\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0c81cd42481197d80547d471a2a7238\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a53240c83d227dd7d48a45bf800cdb21\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f610fcac379a581a5f8497542d8bc04b\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\66b0ccfa6b094f3466bbd551345ed670\\transformed\\jetified-annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2947395e8fab9a91193e5f88065cea3\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ec3eb1e95c63b36e433bc3002b38324\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\26dd23b1db5107ea2a6b2c998df02a06\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\52172a23cc0c229400a65720ef9a6166\\transformed\\jetified-transport-backend-cct-2.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\195f053cd21352b21f8ca89ec0f5b14a\\transformed\\jetified-transport-runtime-2.2.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f65d305b99d54849c78fe711906e83ea\\transformed\\jetified-transport-api-2.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6b28c1d22e327101560835f2c0d522b\\transformed\\jetified-firebase-components-16.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a01d9ab1b0f0fa21c55ac908cb0a0aa3\\transformed\\jetified-firebase-encoders-json-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b5ec95723e3616a277edb5118d4caa3\\transformed\\jetified-firebase-encoders-16.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3027a777f7275d6951a8e61ae8f60a03\\transformed\\exifinterface-1.3.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f77f4ca10c06bd0ab4852effaa3243af\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0fea24358ee5bc4b09b9740bbbd64b31\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\697ef3aafec4246b58c6e73092f613f3\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb2c46fb12030802b25931c1e7cc6c27\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6df012aed8a88dd0a8d20285d34538d6\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8521e63fa3973b03a64640ba8087db9c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c6a28f601256d52ac83d78cff7cef55\\transformed\\jetified-listenablefuture-1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d531023fd29aeab28529d21bfc03a0d\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec6b4f7eedded958dc57536e52e45c61\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7d3cf6280bab24c0706cae5087f7396\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af8eb5634d7d2e3e483c89ceaf30b175\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67e304bc8625f6232022c406ea581cd\\transformed\\jetified-image-1.0.0-beta1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c27e0e5c178156ca47d9e16af9fa57d6\\transformed\\jetified-firebase-annotations-16.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\dalti-provider-flutter\\dalti_provider\\build\\mobile_scanner\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "mobile_scanner_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\BarcodeHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\DeviceOrientationListener.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScanner.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerCallbacks.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerExceptions.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerPermissions.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerPermissionsListener.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerUtilities.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\BarcodeFormats.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\DetectionSpeed.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\MobileScannerErrorCodes.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\MobileScannerStartParameters.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\utils\\DeviceOrientationExtension.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\utils\\Yuv.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-7.0.1\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\utils\\YuvToRgbConverter.kt"