/// Generated file. Do not edit.
///
/// Original file: lib/l10n/app_en.arb
/// To regenerate, run: `flutter gen-l10n`
///
/// Localization for Dalti Provider App
/// Supports: English (en), French (fr), Arabic (ar)

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Dal<PERSON> Fournisseur';

  @override
  String get language => 'Langue';

  @override
  String get languageEnglish => 'English';

  @override
  String get languageFrench => 'Français';

  @override
  String get languageArabic => 'العربية';

  @override
  String get settings => 'Paramètres';

  @override
  String get profile => 'Profil';

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get appointments => 'Rendez-vous';

  @override
  String get appointmentDetails => 'Détails du Rendez-vous';

  @override
  String get calendar => 'Calendrier';

  @override
  String get messages => 'Messages';

  @override
  String get customers => 'clients';

  @override
  String get services => 'Services';

  @override
  String get locations => 'Emplacements';

  @override
  String get save => 'Enregistrer';

  @override
  String get cancel => 'Annuler';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get loading => 'Chargement...';

  @override
  String get error => 'Erreur';

  @override
  String get success => 'Succès';

  @override
  String get languageChanged => 'Langue changée avec succès';

  @override
  String get languageChangeError =>
      'Échec du changement de langue. Veuillez réessayer.';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get currentLanguage => 'Langue actuelle';

  @override
  String get login => 'Connexion';

  @override
  String get logout => 'Déconnexion';

  @override
  String get register => 'S\'inscrire';

  @override
  String get email => 'E-mail';

  @override
  String get password => 'Mot de passe';

  @override
  String get confirmPassword => 'Confirmer le mot de passe';

  @override
  String get firstName => 'Prénom';

  @override
  String get lastName => 'Nom de Famille';

  @override
  String get phone => 'Téléphone';

  @override
  String get businessName => 'Nom de l\'entreprise';

  @override
  String get add => 'Ajouter';

  @override
  String get edit => 'Modifier';

  @override
  String get delete => 'Supprimer';

  @override
  String get update => 'Mettre à jour';

  @override
  String get create => 'Créer';

  @override
  String get search => 'Rechercher';

  @override
  String get filter => 'Filtrer';

  @override
  String get refresh => 'Actualiser';

  @override
  String get back => 'Retour';

  @override
  String get next => 'Suivant';

  @override
  String get previous => 'Précédent';

  @override
  String get close => 'Fermer';

  @override
  String get done => 'Terminé';

  @override
  String get retry => 'Réessayer';

  @override
  String get noData => 'Aucune donnée disponible';

  @override
  String get networkError =>
      'Erreur réseau. Veuillez vérifier votre connexion.';

  @override
  String get serverError => 'Erreur serveur. Veuillez réessayer plus tard.';

  @override
  String get unknownError => 'Une erreur inconnue s\'est produite.';

  @override
  String get validationError => 'Veuillez vérifier votre saisie et réessayer.';

  @override
  String get required => 'Ce champ est obligatoire';

  @override
  String get invalidEmail => 'Veuillez saisir une adresse e-mail valide';

  @override
  String get invalidPhone => 'Veuillez saisir un numéro de téléphone valide';

  @override
  String get passwordTooShort =>
      'Le mot de passe doit contenir au moins 6 caractères';

  @override
  String get passwordsDoNotMatch => 'Les mots de passe ne correspondent pas';

  @override
  String get helpAndSupport => 'Aide et Support';

  @override
  String get theme => 'Thème';

  @override
  String get themeLight => 'Clair';

  @override
  String get themeDark => 'Sombre';

  @override
  String get themeSystem => 'Système';

  @override
  String get logoutConfirmTitle => 'Déconnexion';

  @override
  String get logoutConfirmMessage =>
      'Êtes-vous sûr de vouloir vous déconnecter ?';

  @override
  String get editProfile => 'Modifier le Profil';

  @override
  String get daltiProvider => 'Dalti Provider';

  @override
  String get gettingLocation => 'Obtention de la localisation...';

  @override
  String get useCurrentLocation => 'Utiliser la localisation actuelle';

  @override
  String get notSpecified => 'Non spécifié';

  @override
  String get title => 'Titre';

  @override
  String get category => 'Catégorie';

  @override
  String get profileInformation => 'Informations du Profil';

  @override
  String get aboutMe => 'À Propos de Moi';

  @override
  String get passwordResetSuccessful =>
      'Réinitialisation du Mot de Passe Réussie';

  @override
  String get passwordResetSuccessMessage =>
      'Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.';

  @override
  String get goToLogin => 'Aller à la Connexion';

  @override
  String get failedToResetPassword =>
      'Échec de la réinitialisation du mot de passe. Veuillez réessayer.';

  @override
  String get forgotPassword => 'Mot de Passe Oublié';

  @override
  String get resetPassword => 'Réinitialiser le mot de passe';

  @override
  String get newPassword => 'Nouveau mot de passe';

  @override
  String get confirmNewPassword => 'Confirmer le nouveau mot de passe';

  @override
  String get enterOtp => 'Entrer le Code OTP';

  @override
  String get verifyOtp => 'Vérifier le Code OTP';

  @override
  String get resendOtp => 'Renvoyer le Code OTP';

  @override
  String get otpSent => 'Code OTP envoyé à votre email';

  @override
  String get invalidOtp => 'Code OTP invalide. Veuillez réessayer.';

  @override
  String get welcomeBack => 'Bon Retour';

  @override
  String get signInToContinue => 'Connectez-vous pour continuer';

  @override
  String get dontHaveAccount => 'Vous n\'avez pas de compte ?';

  @override
  String get alreadyHaveAccount => 'Vous avez déjà un compte ?';

  @override
  String get signUp => 'S\'inscrire';

  @override
  String get signIn => 'Se Connecter';

  @override
  String get appointmentsList => 'Rendez-vous';

  @override
  String get notifications => 'Notifications';

  @override
  String get loginSuccessful => 'Connexion réussie !';

  @override
  String get loginFailed => 'Échec de la connexion';

  @override
  String get welcomeToDaltiProvider => 'Bienvenue sur Dalti Fournisseur';

  @override
  String get signInToManageBusiness =>
      'Connectez-vous pour gérer votre entreprise';

  @override
  String get getStarted => 'Commencer';

  @override
  String get skipSetup => 'Ignorer la Configuration ?';

  @override
  String get skipSetupMessage =>
      'Vous pouvez terminer la configuration de votre entreprise plus tard depuis le tableau de bord. Cependant, certaines fonctionnalités peuvent être limitées jusqu\'à ce que la configuration soit terminée.';

  @override
  String get continueSetup => 'Continuer la Configuration';

  @override
  String get skipForNow => 'Ignorer pour l\'instant';

  @override
  String get startConversation => 'Démarrer la Conversation';

  @override
  String get failedToCreateConversation =>
      'Échec de la création de la conversation';

  @override
  String get selectCustomer => 'Sélectionner un client';

  @override
  String get initialMessage => 'Message initial (optionnel) :';

  @override
  String get typeMessageHere => 'Tapez votre message ici...';

  @override
  String get serviceColor => 'Couleur du Service';

  @override
  String get customerManagement => 'Gestion des Clients';

  @override
  String get comingSoon => 'Bientôt Disponible';

  @override
  String get thisFeatureWillAllowYouTo =>
      'Cette fonctionnalité vous permettra de :';

  @override
  String get basicInformation => 'Informations de Base';

  @override
  String get contactInformation => 'Informations de Contact';

  @override
  String get phoneNumber => 'Numéro de Téléphone';

  @override
  String get nationalId => 'Carte d\'Identité';

  @override
  String get notes => 'Notes';

  @override
  String get firstNameRequired => 'Le prénom est requis';

  @override
  String get lastNameRequired => 'Le nom de famille est requis';

  @override
  String get pleaseEnterValidEmail => 'Veuillez entrer un email valide';

  @override
  String get pleaseEnterValidPhone =>
      'Veuillez saisir un numéro de téléphone valide';

  @override
  String get customerEmailHint => '<EMAIL>';

  @override
  String get phoneNumberHint => '+213 123 456 789';

  @override
  String get nationalIdHint => 'Numéro d\'identification optionnel';

  @override
  String get notesHint => 'Notes supplémentaires sur le client';

  @override
  String get activeCustomer => 'Client Actif';

  @override
  String get inactiveCustomer => 'Client Inactif';

  @override
  String get blockedCustomer => 'Client Bloqué';

  @override
  String get serviceTitle => 'Titre du Service';

  @override
  String get serviceTitleHint => 'ex: Consultation, Coupe de Cheveux, Massage';

  @override
  String get serviceTitleRequired => 'Le titre du service est requis';

  @override
  String get titleMinLength => 'Le titre doit contenir au moins 2 caractères';

  @override
  String get creditPointsRequired => 'Points de Crédit Requis';

  @override
  String get creditPointsHint =>
      'Crédits nécessaires pour réserver ce service (minimum 1)';

  @override
  String get creditPointsRequiredError =>
      'L\'exigence de points de crédit est requise';

  @override
  String get creditPointsPositive =>
      'Les points de crédit doivent être un nombre positif';

  @override
  String get deleteService => 'Supprimer le Service';

  @override
  String deleteServiceConfirm(String serviceName) {
    return 'Êtes-vous sûr de vouloir supprimer \"$serviceName\" ?\n\nCette action ne peut pas être annulée.';
  }

  @override
  String get activate => 'Activer';

  @override
  String get deactivate => 'Désactiver';

  @override
  String get minutes => 'minutes';

  @override
  String serviceDeletedSuccessfully(String serviceName) {
    return 'Service \"$serviceName\" supprimé avec succès';
  }

  @override
  String failedToDeleteService(String serviceName) {
    return 'Échec de la suppression du service \"$serviceName\"';
  }

  @override
  String get priceNotSet => 'Prix non défini';

  @override
  String get active => 'Actif';

  @override
  String requiresCreditPoints(int points) {
    return 'Nécessite $points points de crédit pour réserver';
  }

  @override
  String get hours => 'h';

  @override
  String get inactive => 'Inactif';

  @override
  String get queue => 'File d\'Attente';

  @override
  String get queues => 'files d\'attente';

  @override
  String queueCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'files d\'attente',
      one: 'file d\'attente',
    );
    return '$count $_temp0';
  }

  @override
  String get errorLoadingQueues =>
      'Erreur lors du chargement des files d\'attente';

  @override
  String get createLocationsFirstMessage =>
      'Créez d\'abord des emplacements pour gérer les files d\'attente.';

  @override
  String get noQueuesFound => 'Aucune File d\'Attente Trouvée';

  @override
  String get noQueuesForLocation =>
      'Aucune file d\'attente trouvée pour l\'emplacement sélectionné.';

  @override
  String get createFirstQueue =>
      'Créez votre première file d\'attente pour commencer.';

  @override
  String get addQueue => 'Ajouter une File d\'Attente';

  @override
  String get deleteQueue => 'Supprimer la File d\'Attente';

  @override
  String deleteQueueConfirmation(String queueName) {
    return 'Êtes-vous sûr de vouloir supprimer la file d\'attente \"$queueName\"?\\n\\nCette action ne peut pas être annulée.';
  }

  @override
  String customerCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'clients',
      one: 'client',
    );
    return '$count $_temp0';
  }

  @override
  String get clearFilters => 'Effacer les filtres';

  @override
  String get manageCustomerProfiles => 'Gérer les profils clients';

  @override
  String get trackCustomerHistory => 'Suivre l\'historique des clients';

  @override
  String get sendNotifications => 'Envoyer des notifications';

  @override
  String get customerPreferences => 'Préférences des clients';

  @override
  String get loyaltyPrograms => 'Programmes de fidélité';

  @override
  String get call => 'Appeler';

  @override
  String get sms => 'SMS';

  @override
  String get book => 'Réserver';

  @override
  String get editCustomer => 'Modifier Client';

  @override
  String get status => 'Statut';

  @override
  String get wilaya => 'Wilaya';

  @override
  String get dateAdded => 'Date d\'Ajout';

  @override
  String get minimumAppointments => 'Rendez-vous Minimum';

  @override
  String get minimumSpent => 'Dépense Minimum (DA)';

  @override
  String get selectWilaya => 'Sélectionner la wilaya';

  @override
  String get applyFilters => 'Appliquer les Filtres';

  @override
  String get clearAll => 'Tout Effacer';

  @override
  String get searchCustomersPlaceholder =>
      'Rechercher par nom, email ou téléphone...';

  @override
  String get newCustomer => 'Nouveau Client';

  @override
  String get additionalInformation => 'Informations Supplémentaires';

  @override
  String get additionalNotesHint => 'Notes supplémentaires sur le client';

  @override
  String get customerAlreadyExists => 'Le Client Existe Déjà';

  @override
  String get customerExistsInArchive =>
      'Un client avec ces informations existe déjà dans l\'archive.';

  @override
  String get wouldYouLikeToRestore =>
      'Souhaitez-vous restaurer le client existant à la place ?';

  @override
  String get restoreCustomer => 'Restaurer le Client';

  @override
  String customerRestoredSuccessfully(String firstName, String lastName) {
    return 'Client $firstName $lastName restauré avec succès';
  }

  @override
  String failedToRestoreCustomer(String error) {
    return 'Échec de la restauration du client : $error';
  }

  @override
  String customerDeletedSuccessfully(String firstName, String lastName) {
    return 'Client $firstName $lastName supprimé avec succès';
  }

  @override
  String failedToDeleteCustomer(String error) {
    return 'Échec de la suppression du client : $error';
  }

  @override
  String customerUpdatedSuccessfully(String firstName, String lastName) {
    return 'Client $firstName $lastName mis à jour avec succès';
  }

  @override
  String failedToUpdateCustomer(String error) {
    return 'Échec de la mise à jour du client : $error';
  }

  @override
  String get searchAppointmentsPlaceholder => 'Rechercher des rendez-vous...';

  @override
  String get filters => 'Filtres';

  @override
  String get all => 'Tous';

  @override
  String get filterAppointments => 'Filtrer les Rendez-vous';

  @override
  String get customizeAppointmentView =>
      'Personnalisez votre vue des rendez-vous avec des filtres';

  @override
  String get queueFilter => 'Filtre de File d\'Attente';

  @override
  String get pending => 'En Attente';

  @override
  String get scheduled => 'Planifié';

  @override
  String get confirmed => 'Confirmé';

  @override
  String get inProgress => 'En Cours';

  @override
  String get completed => 'Terminé';

  @override
  String get canceled => 'Annulé';

  @override
  String get noShow => 'Absent';

  @override
  String get rescheduled => 'Reprogrammé';

  @override
  String get confirm => 'Confirmer';

  @override
  String get startService => 'Démarrer le Service';

  @override
  String get complete => 'Terminé';

  @override
  String get editAppointment => 'Modifier le Rendez-vous';

  @override
  String get customerDidNotShowUp =>
      'Le client ne s\'est pas présenté au rendez-vous';

  @override
  String get sessionCompletedSuccessfully => 'Session terminée avec succès';

  @override
  String get dateRange => 'Plage de Dates';

  @override
  String get startDate => 'Date de Début';

  @override
  String get endDate => 'Date de Fin';

  @override
  String get statusFilter => 'Filtre de Statut';

  @override
  String get quickSelection => 'Sélection Rapide';

  @override
  String get today => 'Aujourd\'hui';

  @override
  String get thisWeek => 'Cette Semaine';

  @override
  String get thisMonth => 'Ce Mois';

  @override
  String get customRange => 'Plage Personnalisée';

  @override
  String get selectStartDate => 'Sélectionner la Date de Début';

  @override
  String get selectEndDate => 'Sélectionner la Date de Fin';

  @override
  String get clear => 'Effacer';

  @override
  String get statusLabel => 'Statut';

  @override
  String get dateRangeLabel => 'Plage de Dates';

  @override
  String get queueLabel => 'File d\'Attente';

  @override
  String get any => 'Tout';

  @override
  String get customerInformation => 'Informations Client';

  @override
  String get serviceDetails => 'Détails du Service';

  @override
  String get scheduling => 'Planification';

  @override
  String get customerRequired => 'Client *';

  @override
  String get serviceRequired => 'Service *';

  @override
  String get locationRequired => 'Emplacement *';

  @override
  String get queueRequired => 'File d\'Attente *';

  @override
  String get dateRequired => 'Date *';

  @override
  String get timeRangeRequired => 'Plage Horaire *';

  @override
  String get selectService => 'Sélectionner un Service';

  @override
  String get selectLocation => 'Sélectionner un Emplacement';

  @override
  String get selectQueue => 'Sélectionner une File d\'Attente';

  @override
  String get selectLocationFirst => 'Sélectionner d\'abord un emplacement';

  @override
  String get appointmentDate => 'Date du Rendez-vous';

  @override
  String get startTime => 'Heure de Début';

  @override
  String get endTime => 'Heure de Fin';

  @override
  String get notesOptional => 'Notes (Optionnel)';

  @override
  String get addNotesPlaceholder => 'Ajouter des notes supplémentaires...';

  @override
  String get pleaseSelectCustomer => 'Veuillez sélectionner un client';

  @override
  String get pleaseSelectService => 'Veuillez sélectionner un service';

  @override
  String get pleaseSelectLocation => 'Veuillez sélectionner un emplacement';

  @override
  String get pleaseSelectQueue => 'Veuillez sélectionner une file d\'attente';

  @override
  String get startTimeBeforeEndTime =>
      'L\'heure de début doit être antérieure à l\'heure de fin';

  @override
  String get appointmentCreatedSuccessfully => 'Rendez-vous créé avec succès';

  @override
  String get appointmentNumber => 'Rendez-vous #';

  @override
  String get customerLabel => 'Client';

  @override
  String get serviceLabel => 'Service';

  @override
  String get originalTime => 'Heure Originale';

  @override
  String get selectStartTime => 'Sélectionner l\'heure de début';

  @override
  String get selectEndTime => 'Sélectionner l\'heure de fin';

  @override
  String get pleaseSelectLocationFirst =>
      'Veuillez d\'abord sélectionner un emplacement';

  @override
  String get noQueuesAvailable =>
      'Aucune file d\'attente disponible pour l\'emplacement sélectionné';

  @override
  String get errorLoadingLocations =>
      'Erreur lors du chargement des emplacements';

  @override
  String get appointmentNotesOptional => 'Notes de rendez-vous (optionnel)';

  @override
  String get duration => 'Durée';

  @override
  String get time => 'Heure';

  @override
  String get statusScheduled => 'Programmé';

  @override
  String get statusConfirmed => 'Confirmé';

  @override
  String get statusCompleted => 'Terminé';

  @override
  String get statusCanceled => 'Annulé';

  @override
  String get statusNoShow => 'Absent';

  @override
  String get cancelAppointment => 'Annuler le Rendez-vous';

  @override
  String get cancelAppointmentConfirmation =>
      'Êtes-vous sûr de vouloir annuler ce rendez-vous?';

  @override
  String get cancellationReason => 'Raison d\'annulation (optionnel)';

  @override
  String get cancellationReasonHint => 'Entrez la raison de l\'annulation...';

  @override
  String get keepAppointment => 'Garder le Rendez-vous';

  @override
  String get appointmentCancelledSuccessfully =>
      'Rendez-vous annulé avec succès';

  @override
  String get failedToCancelAppointment =>
      'Échec de l\'annulation du rendez-vous';

  @override
  String get calendarSettings => 'Paramètres du Calendrier';

  @override
  String get configureCalendarDisplayPreferences =>
      'Configurer les préférences d\'affichage du calendrier';

  @override
  String get timeSlotInterval => 'Intervalle de Créneaux Horaires';

  @override
  String get selectInterval => 'Sélectionner l\'intervalle';

  @override
  String get timeSlotIntervalDescription =>
      'Ce paramètre détermine en combien de créneaux chaque heure est divisée. Par exemple, 5 minutes créera 12 créneaux par heure, tandis que 15 minutes créera 4 créneaux par heure.';

  @override
  String get apply => 'Appliquer';

  @override
  String get statusAndOverview => 'Statut et Aperçu';

  @override
  String get serviceInformation => 'Informations Service';

  @override
  String get primaryLocation => 'Emplacement Principal';

  @override
  String get cancelAppointmentConfirm => 'Annuler le Rendez-vous';

  @override
  String cancelAppointmentMessage(String customerName) {
    return 'Êtes-vous sûr de vouloir annuler le rendez-vous avec $customerName?';
  }

  @override
  String get completeAppointmentTitle => 'Terminer le Rendez-vous';

  @override
  String completeAppointmentMessage(String customerName) {
    return 'Marquer le rendez-vous avec $customerName comme terminé?';
  }

  @override
  String appointmentConfirmedFor(String customerName) {
    return 'Rendez-vous confirmé pour $customerName';
  }

  @override
  String appointmentCanceledFor(String customerName) {
    return 'Rendez-vous avec $customerName annulé';
  }

  @override
  String get customer => 'Client';

  @override
  String get service => 'Service';

  @override
  String get location => 'Emplacement';

  @override
  String get profileOverview => 'Aperçu du Profil';

  @override
  String get statistics => 'Statistiques';

  @override
  String get rating => 'Note';

  @override
  String get reviews => 'Avis';

  @override
  String get setupStatus => 'Statut de Configuration';

  @override
  String get incomplete => 'Incomplet';

  @override
  String get businessLogo => 'Logo d\'Entreprise';

  @override
  String get provider => 'Prestataire';

  @override
  String get verified => 'Vérifié';

  @override
  String get changeLogo => 'Changer le Logo';

  @override
  String get uploadLogo => 'Télécharger le Logo';

  @override
  String get supportedFormats =>
      'Formats supportés: PNG, JPG, JPEG • Taille max: 5MB';

  @override
  String get noLogoUploaded => 'Aucun logo téléchargé';

  @override
  String get uploadBusinessLogo => 'Télécharger le Logo d\'Entreprise';

  @override
  String get errorLoadingProfile => 'Erreur de Chargement du Profil';

  @override
  String get profileAndBusinessImages => 'Images de Profil et d\'Entreprise';

  @override
  String get professionalTitle => 'Titre Professionnel';

  @override
  String get actions => 'Actions';

  @override
  String get profilePicture => 'Photo de Profil';

  @override
  String get titleRequired => 'Le titre est requis';

  @override
  String get phoneRequired => 'Le numéro de téléphone est requis';

  @override
  String get noCategorySelected => 'Aucune catégorie sélectionnée';

  @override
  String get categoryCannotBeChanged =>
      'La catégorie ne peut pas être modifiée';

  @override
  String get saving => 'Enregistrement...';

  @override
  String get saveChanges => 'Enregistrer les Modifications';

  @override
  String get profileUpdatedSuccessfully => 'Profil mis à jour avec succès';

  @override
  String failedToUpdateProfile(String error) {
    return 'Échec de la mise à jour du profil: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully =>
      'Photo de profil mise à jour avec succès!';

  @override
  String get profileCompletion => 'Complétude du Profil';

  @override
  String get overallProgress => 'Progrès Global';

  @override
  String percentComplete(int percentage) {
    return '$percentage% Terminé';
  }

  @override
  String get details => 'Détails';

  @override
  String get profilePictureItem => 'Photo de Profil';

  @override
  String get providerInfo => 'Informations Prestataire';

  @override
  String get nextSteps => 'Prochaines Étapes';

  @override
  String get failedToLoadProfileCompletion =>
      'Échec du chargement de la complétude du profil';

  @override
  String get uploadProfessionalProfilePicture =>
      'Télécharger une photo de profil professionnelle';

  @override
  String get completeProviderInformation =>
      'Compléter les informations prestataire : description d\'entreprise';

  @override
  String get addAtLeastOneServiceLocation =>
      'Ajouter au moins un lieu de service';

  @override
  String get createAtLeastOneServiceOffering =>
      'Créer au moins une offre de service';

  @override
  String get setUpBookingQueuesTimeSlots =>
      'Configurer les files d\'attente/créneaux de réservation';

  @override
  String get customerProfile => 'Profil Client';

  @override
  String get deleteCustomer => 'Supprimer Client';

  @override
  String deleteCustomerConfirmation(String firstName, String lastName) {
    return 'Êtes-vous sûr de vouloir supprimer $firstName $lastName ? Cette action ne peut pas être annulée.';
  }

  @override
  String get information => 'Informations';

  @override
  String get mobile => 'Mobile';

  @override
  String get customerSince => 'Client Depuis';

  @override
  String get customerStatistics => 'Statistiques Client';

  @override
  String get totalSpent => 'Total Dépensé';

  @override
  String get lastVisit => 'Dernière Visite';

  @override
  String get never => 'Jamais';

  @override
  String get recentAppointments => 'Rendez-vous Récents';

  @override
  String get noRecentAppointments => 'Aucun rendez-vous récent';

  @override
  String get addAppointmentFeatureComingSoon =>
      'Fonctionnalité d\'ajout de rendez-vous bientôt disponible';

  @override
  String get loadingDashboard => 'Chargement du tableau de bord...';

  @override
  String get failedToLoadDashboard => 'Échec du chargement du tableau de bord';

  @override
  String get unknownErrorOccurred => 'Une erreur inconnue s\'est produite';

  @override
  String get refreshingData => 'Actualisation des données...';

  @override
  String get failedToLoadActiveSessions =>
      'Échec du chargement des sessions actives';

  @override
  String get failedToLoadPendingAppointments =>
      'Échec du chargement des rendez-vous en attente';

  @override
  String get processingEmergencyControl =>
      'Traitement du contrôle d\'urgence...';

  @override
  String get refreshService => 'Actualiser le Service';

  @override
  String get editService => 'Modifier le Service';

  @override
  String get locationName => 'Nom de l\'Emplacement';

  @override
  String get locationNameHint =>
      'ex: Bureau Principal, Succursale Centre-ville';

  @override
  String get locationNameRequired => 'Le nom de l\'emplacement est requis';

  @override
  String get locationNameMinLength =>
      'Le nom de l\'emplacement doit contenir au moins 2 caractères';

  @override
  String get locationNameMaxLength =>
      'Le nom de l\'emplacement doit contenir moins de 100 caractères';

  @override
  String get streetAddress => 'Adresse de la Rue';

  @override
  String get streetAddressHint => 'ex: 123 Rue Principale';

  @override
  String get streetAddressRequired => 'L\'adresse de la rue est requise';

  @override
  String get pleaseEnterCompleteAddress =>
      'Veuillez saisir une adresse complète';

  @override
  String get addressMaxLength =>
      'L\'adresse doit contenir moins de 200 caractères';

  @override
  String get country => 'Pays';

  @override
  String get algeria => 'Algérie';

  @override
  String get monday => 'Lundi';

  @override
  String get tuesday => 'Mardi';

  @override
  String get wednesday => 'Mercredi';

  @override
  String get thursday => 'Jeudi';

  @override
  String get friday => 'Vendredi';

  @override
  String get saturday => 'Samedi';

  @override
  String get sunday => 'Dimanche';

  @override
  String get activeServiceSessions => 'Sessions de Service Actives';

  @override
  String get viewAll => 'Voir Tout';

  @override
  String get noActiveSessions => 'Aucune Session Active';

  @override
  String get noActiveSessionsDescription =>
      'Il n\'y a aucune session de service active pour le moment';

  @override
  String get pendingAppointments => 'Rendez-vous en Attente';

  @override
  String get allCaughtUp => 'Tout est à Jour !';

  @override
  String get noPendingAppointments => 'Aucun rendez-vous en attente à examiner';

  @override
  String get todaysSchedule => 'Programme d\'Aujourd\'hui';

  @override
  String get unableToRefreshData =>
      'Impossible d\'actualiser les données. Affichage des informations en cache.';

  @override
  String get goodMorning => 'Bonjour';

  @override
  String get goodAfternoon => 'Bon après-midi';

  @override
  String get goodEvening => 'Bonsoir';

  @override
  String get waiting => 'en attente';

  @override
  String moreQueues(int count) {
    return '+$count files d\'attente supplémentaires';
  }

  @override
  String get noActiveQueues => 'Aucune file d\'attente active';

  @override
  String get total => 'Total';

  @override
  String get upcoming => 'À venir';

  @override
  String appointmentsAwaitingAction(int count, String plural) {
    return '$count rendez-vous$plural en attente d\'action';
  }

  @override
  String get quickActions => 'Actions Rapides';

  @override
  String get newService => 'Nouveau Service';

  @override
  String get newQueue => 'Nouvelle File';

  @override
  String get newAppointment => 'Nouveau Rendez-vous';

  @override
  String get noUpcomingAppointments => 'Aucun rendez-vous à venir';

  @override
  String get allAppointmentsCompleted =>
      'Tous les rendez-vous d\'aujourd\'hui sont terminés';

  @override
  String get todaysSummary => 'Résumé d\'Aujourd\'hui';

  @override
  String updatedSecondsAgo(int seconds) {
    return 'Mis à jour il y a ${seconds}s';
  }

  @override
  String updatedMinutesAgo(int minutes) {
    return 'Mis à jour il y a ${minutes}m';
  }

  @override
  String updatedHoursAgo(int hours) {
    return 'Mis à jour il y a ${hours}h';
  }

  @override
  String updatedDaysAgo(int days) {
    return 'Mis à jour il y a ${days}j';
  }

  @override
  String get justNow => 'À l\'instant';

  @override
  String get dataRefresh => 'Actualisation des Données';

  @override
  String lastUpdated(String timeAgo) {
    return 'Dernière mise à jour : $timeAgo';
  }

  @override
  String get byLocation => 'Par Emplacement';

  @override
  String get allQueues => 'Toutes les Files';

  @override
  String get scheduleManagement => 'Gestion des Horaires';

  @override
  String get weeklyView => 'Vue Hebdomadaire';

  @override
  String get listView => 'Vue Liste';

  @override
  String get appSettings => 'Paramètres de l\'App';

  @override
  String get account => 'Compte';

  @override
  String get management => 'Gestion';

  @override
  String get manageServiceOfferings => 'Gérez vos offres de services';

  @override
  String get configureBusinessLocations =>
      'Configurez les emplacements d\'entreprise';

  @override
  String get setupAppointmentQueues => 'Configurez les files de rendez-vous';

  @override
  String get getHelpAndSupport => 'Obtenez de l\'aide et du support';

  @override
  String get signOutOfAccount => 'Déconnectez-vous de votre compte';

  @override
  String get queueManagement => 'Gestion des Files';

  @override
  String get createService => 'Créer un Service';

  @override
  String get createNewService => 'Créer un Nouveau Service';

  @override
  String get addNewServiceDescription =>
      'Ajoutez un nouveau service à vos offres commerciales';

  @override
  String get serviceAvailableInfo =>
      'Votre service sera disponible à la réservation une fois créé. Vous pourrez le modifier ou le désactiver plus tard depuis la liste des services.';

  @override
  String get creatingService => 'Création du Service...';

  @override
  String get description => 'Description';

  @override
  String get describeYourService => 'Décrivez votre service';

  @override
  String get minutesShort => 'm';

  @override
  String get hoursShort => 'h';

  @override
  String get createLocation => 'Créer un Emplacement';

  @override
  String get nameMinLength => 'Le nom doit contenir au moins 2 caractères';

  @override
  String get nameMaxLength => 'Le nom doit contenir moins de 100 caractères';

  @override
  String get shortName => 'Nom Court';

  @override
  String get shortNameHint => 'ex: Bureau Principal';

  @override
  String get shortNameMaxLength =>
      'Le nom court doit contenir moins de 100 caractères';

  @override
  String get city => 'Ville';

  @override
  String get cityRequired => 'La ville est requise';

  @override
  String get selectValidCity =>
      'Veuillez sélectionner une ville algérienne valide';

  @override
  String get selectAlgerianCity => 'Sélectionner une ville algérienne';

  @override
  String get countryRequired => 'Le pays est requis';

  @override
  String get onlyAvailableInAlgeria =>
      'Actuellement disponible uniquement en Algérie';

  @override
  String locationCreatedSuccessfully(String locationName) {
    return 'Emplacement \"$locationName\" créé avec succès';
  }

  @override
  String get failedToCreateLocation => 'Échec de la création de l\'emplacement';

  @override
  String locationUpdatedSuccessfully(String locationName) {
    return 'Emplacement \"$locationName\" mis à jour avec succès';
  }

  @override
  String get failedToUpdateLocation =>
      'Échec de la mise à jour de l\'emplacement';

  @override
  String get createNewLocation => 'Créer un Nouvel Emplacement';

  @override
  String get addNewBusinessLocation =>
      'Ajouter un nouvel emplacement commercial avec adresse et heures d\'ouverture';

  @override
  String get locationInformation => 'Informations de l\'Emplacement';

  @override
  String get address => 'Adresse';

  @override
  String get addressHint => 'ex: 123 Rue Principale, Bâtiment A';

  @override
  String get addressRequired => 'L\'adresse est requise';

  @override
  String get postalCode => 'Code Postal';

  @override
  String get postalCodeHint => 'ex: 16000 (optionnel)';

  @override
  String get timezone => 'Fuseau Horaire';

  @override
  String get timezoneRequired => 'Le fuseau horaire est requis';

  @override
  String get locationCoordinates => 'Coordonnées de l\'Emplacement';

  @override
  String get getCurrentLocationDescription =>
      'Obtenez votre localisation actuelle pour aider les clients à vous trouver facilement';

  @override
  String get latitude => 'Latitude';

  @override
  String get longitude => 'Longitude';

  @override
  String get willBeFilledAutomatically => 'Sera rempli automatiquement';

  @override
  String get pleaseGetCurrentLocation =>
      'Veuillez obtenir la position actuelle';

  @override
  String get invalidLatitude => 'Latitude invalide';

  @override
  String get invalidLongitude => 'Longitude invalide';

  @override
  String get latitudeMustBeBetween => 'La latitude doit être entre -90 et 90';

  @override
  String get longitudeMustBeBetween =>
      'La longitude doit être entre -180 et 180';

  @override
  String get getCurrentLocation => 'Obtenir la Localisation Actuelle';

  @override
  String get amenities => 'Commodités';

  @override
  String get parkingAvailable => 'Parking Disponible';

  @override
  String get onsiteParkingForCustomers => 'Parking sur site pour les clients';

  @override
  String get elevatorAccess => 'Accès Ascenseur';

  @override
  String get buildingHasElevatorAccess =>
      'Le bâtiment dispose d\'un accès par ascenseur';

  @override
  String get wheelchairAccessible => 'Accessible en Fauteuil Roulant';

  @override
  String get accessibleForPeopleWithDisabilities =>
      'Accessible aux personnes handicapées';

  @override
  String get openingHours => 'Heures d\'Ouverture';

  @override
  String get closed => 'Fermé';

  @override
  String get open => 'Ouvert';

  @override
  String get tipToggleSwitchDayOpenClosed =>
      'Astuce : Basculez l\'interrupteur pour marquer un jour comme ouvert ou fermé';

  @override
  String get locationWillBeAddedToBusinessLocations =>
      'Cet emplacement sera ajouté à vos emplacements commerciaux. Vous pouvez gérer tous les emplacements depuis la section emplacements';

  @override
  String get locationAvailableForQueueManagement =>
      'Votre emplacement sera disponible pour la gestion des files d\'attente et des rendez-vous une fois créé. Assurez-vous de définir des coordonnées précises et des heures d\'ouverture';

  @override
  String get creatingLocation => 'Création de l\'emplacement...';

  @override
  String get editLocation => 'Modifier l\'Emplacement';

  @override
  String get updateLocationDetails =>
      'Mettre à jour les détails de l\'emplacement, l\'adresse et les heures d\'ouverture';

  @override
  String get updateCoordinatesDescription =>
      'Mettre à jour les coordonnées si l\'emplacement a déménagé pour aider les clients à vous trouver facilement';

  @override
  String get changesWillBeUpdatedAcrossServices =>
      'Les modifications de cet emplacement seront mises à jour dans tous vos services commerciaux et files d\'attente';

  @override
  String get changesWillBeSavedImmediately =>
      'Les modifications seront sauvegardées immédiatement et reflétées dans vos paramètres d\'emplacement. Les files d\'attente et rendez-vous existants ne seront pas affectés';

  @override
  String get savingChanges => 'Enregistrement des modifications...';

  @override
  String get updateCurrentLocation => 'Mettre à Jour l\'Emplacement Actuel';

  @override
  String get fax => 'Fax';

  @override
  String get floor => 'Étage';

  @override
  String get mobileHint => 'ex., +213 555-123456';

  @override
  String get faxHint => 'ex., +213 555-123456';

  @override
  String get floorHint => 'ex., 5ème Étage';

  @override
  String get createQueue => 'Créer la File';

  @override
  String get editQueue => 'Modifier la File d\'Attente';

  @override
  String get queueInformation => 'Informations de la File d\'Attente';

  @override
  String get queueName => 'Nom de la File d\'Attente';

  @override
  String get queueNameHint => 'ex., File Générale, File VIP, Sans rendez-vous';

  @override
  String get queueServices => 'Services de la File d\'Attente';

  @override
  String get selectServicesDescription =>
      'Sélectionnez les services qui seront disponibles dans cette file d\'attente';

  @override
  String get selectAll => 'Tout Sélectionner';

  @override
  String servicesSelected(int count, int total) {
    return '$count sur $total sélectionnés';
  }

  @override
  String get queueNameRequired => 'Le nom de la file est requis';

  @override
  String get queueNameMinLength => 'Le nom doit contenir au moins 2 caractères';

  @override
  String get queueNameMaxLength =>
      'Le nom de la file d\'attente ne peut pas dépasser 100 caractères';

  @override
  String get loadingServices => 'Chargement des services...';

  @override
  String get credits => 'crédits';

  @override
  String get pleaseSelectAtLeastOneService =>
      'Veuillez sélectionner au moins un service';

  @override
  String get queueOperatingHours => 'Heures d\'Ouverture de la File d\'Attente';

  @override
  String get setQueueAvailabilityDescription =>
      'Définissez quand cette file d\'attente est disponible pour les rendez-vous';

  @override
  String get noServicesAvailable => 'Aucun service disponible';

  @override
  String get createServicesFirstMessage =>
      'Créez d\'abord des services pour les assigner aux files d\'attente.';

  @override
  String get addServices => 'Ajouter des Services';

  @override
  String get serviceDeliveryType => 'Type de Livraison du Service';

  @override
  String get atBusinessLocation => 'Au Local Commercial';

  @override
  String get atCustomerLocation => 'Chez le Client';

  @override
  String get bothOptions => 'Les Deux Emplacements';

  @override
  String get selectDeliveryType => 'Veuillez sélectionner un type de livraison';

  @override
  String get selectRegions => 'Sélectionner les régions...';

  @override
  String regionsSelected(int count) {
    return '$count région(s) sélectionnée(s)';
  }

  @override
  String get customColor => 'Couleur Personnalisée';

  @override
  String get invalidHexColor => 'Couleur hexadécimale invalide';

  @override
  String editServiceTitle(String serviceName) {
    return 'Modifier $serviceName';
  }

  @override
  String updateServiceDescription(String serviceName) {
    return 'Mettre à jour les détails et paramètres de \"$serviceName\"';
  }

  @override
  String get changesWillBeSaved =>
      'Les modifications seront sauvegardées immédiatement et reflétées dans vos offres de services. Les rendez-vous existants ne seront pas affectés.';

  @override
  String errorLoadingService(String error) {
    return 'Erreur lors du chargement du service : $error';
  }

  @override
  String errorRefreshingService(String error) {
    return 'Erreur lors de l\'actualisation du service : $error';
  }

  @override
  String serviceUpdatedSuccessfully(String serviceName) {
    return 'Service \"$serviceName\" mis à jour avec succès';
  }

  @override
  String get failedToUpdateService => 'Échec de la mise à jour du service';

  @override
  String errorUpdatingService(String error) {
    return 'Erreur lors de la mise à jour du service : $error';
  }

  @override
  String get price => 'Prix (DA)';

  @override
  String get priceRequired => 'Le prix est requis';

  @override
  String get enterValidPrice => 'Entrez un prix valide';

  @override
  String get serviceDelivery => 'Livraison du Service';

  @override
  String get whereProvideService => 'Où fournissez-vous ce service ?';

  @override
  String get appearance => 'Apparence';

  @override
  String get serviceOptions => 'Options du Service';

  @override
  String get publicService => 'Service Public';

  @override
  String get visibleToAllCustomers => 'Visible par tous les clients';

  @override
  String get acceptOnlineBookings => 'Accepter les Réservations en Ligne';

  @override
  String get allowCustomersBookOnline =>
      'Permettre aux clients de réserver en ligne';

  @override
  String get acceptNewCustomers => 'Accepter les Nouveaux Clients';

  @override
  String get allowNewCustomersBook =>
      'Permettre aux nouveaux clients de réserver';

  @override
  String get enableNotifications => 'Activer les Notifications';

  @override
  String get getNotifiedNewBookings =>
      'Être notifié des nouvelles réservations';

  @override
  String get serviceAvailableForBooking =>
      'Le service est disponible à la réservation';

  @override
  String get selectServedRegionsError =>
      'Veuillez sélectionner les régions desservies pour les services à domicile';

  @override
  String get themeModeLight => 'Clair';

  @override
  String get themeModeDark => 'Sombre';

  @override
  String get themeModeSystem => 'Système';

  @override
  String get searchServices => 'Rechercher des services...';

  @override
  String get tryAdjustingSearchTerms =>
      'Essayez d\'ajuster vos termes de recherche';

  @override
  String get noServicesFound => 'Aucun service trouvé';

  @override
  String get errorLoadingServices => 'Erreur lors du chargement des services';

  @override
  String get noLocationsFound => 'Aucun emplacement trouvé';

  @override
  String get failedToLoadLocations => 'Échec du chargement des emplacements';

  @override
  String get noLocationsAvailable => 'Aucun emplacement disponible';

  @override
  String get noCustomersFound => 'Aucun client trouvé';

  @override
  String get failedToLoadCustomers => 'Échec du chargement des clients';

  @override
  String get addFirstCustomerToGetStarted =>
      'Ajoutez votre premier client pour commencer';

  @override
  String get addCustomer => 'Ajouter Client';

  @override
  String get noAppointmentsFound => 'Aucun rendez-vous trouvé';

  @override
  String get tryAdjustingYourFilters => 'Essayez d\'ajuster vos filtres';

  @override
  String get createFirstAppointmentToGetStarted =>
      'Créez votre premier rendez-vous pour commencer';

  @override
  String get addAppointment => 'Ajouter Rendez-vous';

  @override
  String get noAppointmentsScheduled => 'Aucun rendez-vous programmé';

  @override
  String get tapPlusButtonToAddAppointment =>
      'Appuyez sur le bouton + pour ajouter un rendez-vous';

  @override
  String get tapToChange => 'Appuyez pour changer';

  @override
  String get emailOrPhone => 'Email ou Téléphone';

  @override
  String get enterEmailOrPhone => 'Entrez votre email ou numéro de téléphone';

  @override
  String get pleaseEnterEmailOrPhone =>
      'Veuillez entrer votre email ou téléphone';

  @override
  String get pleaseEnterPassword => 'Veuillez entrer votre mot de passe';

  @override
  String get joinDaltiProvider => 'Rejoindre Dalti Provider';

  @override
  String get createBusinessAccount => 'Créez votre compte professionnel';

  @override
  String get selectBusinessCategory =>
      'Sélectionnez votre catégorie d\'entreprise';

  @override
  String get createAccount => 'Créer un compte';

  @override
  String get pleaseEnterBusinessName =>
      'Veuillez entrer le nom de votre entreprise';

  @override
  String get businessCategory => 'Catégorie d\'entreprise';

  @override
  String get pleaseEnterEmail => 'Veuillez entrer votre email';

  @override
  String get pleaseEnterPhoneNumber =>
      'Veuillez entrer votre numéro de téléphone';

  @override
  String get pleaseConfirmPassword => 'Veuillez confirmer votre mot de passe';

  @override
  String get resetPasswordDescription =>
      'Entrez votre adresse email et nous vous enverrons un code de vérification pour réinitialiser votre mot de passe';

  @override
  String get emailAddress => 'Adresse email';

  @override
  String get enterEmailAddress => 'Entrez votre adresse email';

  @override
  String get pleaseEnterEmailAddress => 'Veuillez entrer votre adresse email';

  @override
  String get pleaseEnterValidEmailAddress =>
      'Veuillez entrer une adresse email valide';

  @override
  String get sendResetCode => 'Envoyer le code de réinitialisation';

  @override
  String get backToLogin => 'Retour à la connexion';

  @override
  String get resetCodeSent => 'Code de réinitialisation envoyé !';

  @override
  String resetCodeSentDescription(String email) {
    return 'Nous avons envoyé un code de vérification à $email';
  }

  @override
  String get continueToVerification => 'Continuer vers la vérification';

  @override
  String get resendCode => 'Renvoyer le code';

  @override
  String resendCodeIn(int seconds) {
    return 'Renvoyer le code dans ${seconds}s';
  }

  @override
  String get verifyResetCode => 'Vérifier le code de réinitialisation';

  @override
  String verifyResetCodeDescription(String email) {
    return 'Nous avons envoyé un code à 6 chiffres à\n$email';
  }

  @override
  String get enterVerificationCode => 'Entrez le code de vérification';

  @override
  String codeExpiresIn(String minutes, String seconds) {
    return 'Le code expire dans $minutes:$seconds';
  }

  @override
  String get codeHasExpired => 'Le code a expiré';

  @override
  String get verifyCode => 'Vérifier le code';

  @override
  String get resendCodeWhenExpired => 'Renvoyer le code quand expiré';

  @override
  String get backToEmailEntry => 'Retour à la saisie d\'email';

  @override
  String get createNewPassword => 'Créer un nouveau mot de passe';

  @override
  String get createNewPasswordDescription =>
      'Veuillez créer un mot de passe fort pour votre compte';

  @override
  String resetTokenExpiresIn(int minutes) {
    return 'Le jeton de réinitialisation expire dans $minutes minutes';
  }

  @override
  String get resetTokenExpired =>
      'Le jeton de réinitialisation a expiré. Veuillez demander une nouvelle réinitialisation de mot de passe.';

  @override
  String get enterNewPassword => 'Entrez votre nouveau mot de passe';

  @override
  String get confirmNewPasswordHint => 'Confirmez votre nouveau mot de passe';

  @override
  String get passwordRequirements => 'Exigences du mot de passe';

  @override
  String get atLeast8Characters => 'Au moins 8 caractères';

  @override
  String get containsLowercaseLetter => 'Contient une lettre minuscule';

  @override
  String get containsUppercaseLetter => 'Contient une lettre majuscule';

  @override
  String get containsNumber => 'Contient un chiffre';

  @override
  String get containsSpecialCharacter => 'Contient un caractère spécial';

  @override
  String get resetPasswordButton => 'Réinitialiser le mot de passe';

  @override
  String get passwordDoesNotMeetRequirements =>
      'Le mot de passe ne répond pas aux exigences';

  @override
  String get createFirstService => 'Créez votre premier service pour commencer';

  @override
  String get addService => 'Ajouter Service';

  @override
  String get searchLocations => 'Rechercher des emplacements...';

  @override
  String get createFirstLocation =>
      'Créez votre premier emplacement pour commencer';

  @override
  String get addLocation => 'Ajouter un Emplacement';

  @override
  String get schedulingInformation => 'Informations de Planification';

  @override
  String get date => 'Date';

  @override
  String get unknown => 'Inconnu';

  @override
  String get businessSetup => 'Configuration d\'Entreprise';

  @override
  String get skipSetupTooltip => 'Skip setup';

  @override
  String get businessInformation => 'Informations d\'Entreprise';

  @override
  String get tellUsAboutYourBusiness => 'Parlez-nous de votre entreprise';

  @override
  String get businessNameRequired => 'Le nom de l\'entreprise est requis';

  @override
  String get businessNameMinLength =>
      'Le nom de l\'entreprise doit contenir au moins 2 caractères';

  @override
  String get enterYourBusinessName => 'Entrez le nom de votre entreprise';

  @override
  String get businessDescription => 'Description de l\'Entreprise';

  @override
  String get businessDescriptionRequired =>
      'La description de l\'entreprise est requise';

  @override
  String get businessDescriptionMinLength =>
      'La description doit contenir au moins 10 caractères';

  @override
  String get describeWhatYourBusinessDoes =>
      'Décrivez ce que fait votre entreprise';

  @override
  String get businessCategoryRequired =>
      'Veuillez sélectionner une catégorie d\'entreprise';

  @override
  String get shortNameOptional => 'Nom Court (Optionnel)';

  @override
  String get businessLogoOptional => 'Logo d\'Entreprise (Optionnel)';

  @override
  String get uploadBusinessLogoDescription =>
      'Téléchargez le logo de votre entreprise pour aider les clients à reconnaître votre marque';

  @override
  String get clickToUploadLogo => 'Cliquez pour télécharger le logo';

  @override
  String get businessInfoDescription =>
      'This information will be displayed to your customers and used to set up your business profile.';

  @override
  String get onboardingWelcomeTitle => 'Bienvenue sur Dalti Provider !';

  @override
  String get onboardingWelcomeSubtitle =>
      'Configurons votre profil d\'entreprise et préparons-vous à servir vos clients.';

  @override
  String get onboardingSetup => 'Configuration';

  @override
  String get onboardingSkipForNow => 'Ignorer pour l\'instant';

  @override
  String get onboardingTimeEstimate =>
      'Prend environ 5 à 10 minutes à compléter';

  @override
  String get onboardingSetupSteps => 'Étapes de Configuration';

  @override
  String get onboardingStepBusinessInfo => 'Informations d\'Entreprise';

  @override
  String get onboardingStepBusinessInfoDesc =>
      'Parlez-nous de votre entreprise et de vos coordonnées';

  @override
  String get onboardingStepLocationSetup => 'Configuration d\'Emplacement';

  @override
  String get onboardingStepLocationSetupDesc =>
      'Ajoutez vos emplacements d\'entreprise et heures d\'ouverture';

  @override
  String get onboardingStepServiceCreation => 'Création de Services';

  @override
  String get onboardingStepServiceCreationDesc =>
      'Définissez les services que vous offrez aux clients';

  @override
  String get onboardingStepQueueManagement => 'Gestion des Files d\'Attente';

  @override
  String get onboardingStepQueueManagementDesc =>
      'Configurez les files d\'attente pour organiser vos services';

  @override
  String get onboardingWhyUseDalti => 'Pourquoi utiliser Dalti Provider ?';

  @override
  String get onboardingBenefitManageCustomers => 'Gérer les Clients';

  @override
  String get onboardingBenefitManageCustomersDesc =>
      'Organisez et suivez efficacement les rendez-vous de vos clients';

  @override
  String get onboardingBenefitSmartScheduling => 'Planification Intelligente';

  @override
  String get onboardingBenefitSmartSchedulingDesc =>
      'Gestion automatisée des files d\'attente et planification des rendez-vous';

  @override
  String get onboardingBenefitBusinessInsights => 'Aperçus d\'Entreprise';

  @override
  String get onboardingBenefitBusinessInsightsDesc =>
      'Suivez les performances et développez votre entreprise avec des analyses';

  @override
  String get onboardingBenefitMobileReady => 'Prêt pour Mobile';

  @override
  String get onboardingBenefitMobileReadyDesc =>
      'Accédez à votre tableau de bord d\'entreprise n\'importe où, n\'importe quand';

  @override
  String get onboardingStepWelcome => 'Bienvenue';

  @override
  String get onboardingStepWelcomeDesc => 'Bienvenue sur Dalti Provider';

  @override
  String get onboardingStepBusinessProfile => 'Info Entreprise';

  @override
  String get onboardingStepBusinessProfileDesc =>
      'Parlez-nous de votre entreprise';

  @override
  String get onboardingStepLocation => 'Emplacement';

  @override
  String get onboardingStepLocationDesc =>
      'Ajoutez votre emplacement d\'entreprise';

  @override
  String get onboardingStepServices => 'Services';

  @override
  String get onboardingStepServicesDesc => 'Définissez vos services';

  @override
  String get onboardingStepQueues => 'Files d\'Attente';

  @override
  String get onboardingStepQueuesDesc => 'Configurez vos files d\'attente';

  @override
  String get onboardingStepSummary => 'Résumé';

  @override
  String get onboardingStepSummaryDesc => 'Examinez votre configuration';

  @override
  String get onboardingStepCompleted => 'Terminé';

  @override
  String get onboardingStepCompletedDesc => 'Configuration terminée !';

  @override
  String get onboardingPrimaryLocation => 'Emplacement Principal';

  @override
  String get otpVerificationSuccessTitle => 'OTP Vérifié avec Succès !';

  @override
  String get otpVerificationSuccessMessage =>
      'Votre identité a été vérifiée. Vous pouvez maintenant procéder à la réinitialisation de votre mot de passe.';

  @override
  String get continueToResetPassword => 'Continuer';

  @override
  String get welcomeToApp => 'Bienvenue sur';

  @override
  String get welcomeSubtitle =>
      'Votre solution complète de gestion d\'entreprise';

  @override
  String get welcomeDescription =>
      'Rationalisez vos opérations commerciales avec notre plateforme complète conçue pour les prestataires de services. Gérez les rendez-vous, les files d\'attente, les clients et développez votre entreprise efficacement.';

  @override
  String get getStartedNow => 'Commencer';

  @override
  String get signInHere => 'Se Connecter';

  @override
  String get welcomeFeatureManageAppointments => 'Gérer les Rendez-vous';

  @override
  String get welcomeFeatureManageAppointmentsDesc =>
      'Planifiez et suivez facilement les rendez-vous clients';

  @override
  String get welcomeFeatureQueueManagement => 'Gestion des Files d\'Attente';

  @override
  String get welcomeFeatureQueueManagementDesc =>
      'Organisez les services et réduisez les temps d\'attente';

  @override
  String get welcomeFeatureBusinessInsights => 'Analyses d\'Entreprise';

  @override
  String get welcomeFeatureBusinessInsightsDesc =>
      'Suivez les performances et prenez des décisions basées sur les données';

  @override
  String get switchToLightMode => 'Passer au Mode Clair';

  @override
  String get switchToDarkMode => 'Passer au Mode Sombre';

  @override
  String get termsAndPrivacyText =>
      'En continuant, vous acceptez nos Conditions de Service et notre Politique de Confidentialité.';

  @override
  String get welcomeTitle => 'Bienvenue sur Dalti';

  @override
  String get welcomeMessage =>
      'Gérez votre planning et vos clients en toute simplicité. Connectez-vous avec les clients, prenez des rendez-vous.';

  @override
  String get logIn => 'Se Connecter';

  @override
  String get completingSetup => 'Finalisation de la configuration...';

  @override
  String get businessProfileInfoCard =>
      'Ces informations seront affichées à vos clients et utilisées pour configurer votre profil d\'entreprise.';

  @override
  String yourServicesCount(int count) {
    return 'Vos Services ($count)';
  }

  @override
  String get addYourFirstService => 'Ajouter Votre Premier Service';

  @override
  String get addAnotherService => 'Ajouter Un Autre Service';

  @override
  String get addServiceInfoMessage =>
      'Ajoutez au moins un service pour continuer. Vous pourrez ajouter plus de services plus tard depuis la section de gestion des services.';

  @override
  String get online => 'En Ligne';

  @override
  String get newClients => 'Nouveaux Clients';

  @override
  String get addNewService => 'Ajouter Nouveau Service';

  @override
  String get serviceTitleMinLength =>
      'Le titre doit contenir au moins 2 caractères';

  @override
  String get serviceDescription => 'Description';

  @override
  String get serviceDescriptionHint => 'Décrivez votre service';

  @override
  String get durationMinutes => 'Durée (minutes)';

  @override
  String get priceDA => 'Prix (DA)';

  @override
  String get allowCustomersToBookOnline =>
      'Permettre aux clients de réserver en ligne';

  @override
  String get allowNewCustomersToBook =>
      'Permettre aux nouveaux clients de réserver';

  @override
  String get getNotifiedForNewBookings =>
      'Être notifié des nouvelles réservations';

  @override
  String get creditPointsPositiveError =>
      'Les points de crédit doivent être un nombre positif';

  @override
  String get bothLocations => 'Les Deux Emplacements';

  @override
  String get pleaseSelectDeliveryType =>
      'Veuillez sélectionner un type de livraison';

  @override
  String get servedRegions => 'Régions Desservies';

  @override
  String get chooseWilayasHint =>
      'Choisissez les wilayas où vous fournissez des services';

  @override
  String get searchWilayasHint => 'Rechercher des wilayas...';

  @override
  String get selectAtLeastOneWilaya =>
      'Veuillez sélectionner au moins une wilaya pour les services chez le client';

  @override
  String serviceAddedSuccessfully(String serviceName) {
    return 'Service \"$serviceName\" ajouté avec succès !';
  }

  @override
  String get removeService => 'Supprimer le Service';

  @override
  String removeServiceConfirmation(String serviceName) {
    return 'Êtes-vous sûr de vouloir supprimer \"$serviceName\" ?';
  }

  @override
  String get uploadYourBusinessLogo =>
      'Téléchargez le logo de votre entreprise pour aider les clients à reconnaître votre marque';

  @override
  String stepCounter(int current, int total) {
    return '$current/$total';
  }

  @override
  String get chooseImageFromDevice =>
      'Choisissez un fichier image depuis votre appareil';

  @override
  String get supportedFormatsInfo =>
      'Formats supportés: PNG, JPG, JPEG • Taille max: 5MB';

  @override
  String get uploadingLogo => 'Téléchargement du logo...';

  @override
  String get logoUploadedSuccessfully => 'Logo téléchargé avec succès!';

  @override
  String get logoRemovedSuccessfully => 'Logo supprimé avec succès!';

  @override
  String get failedToReadFileData =>
      'Échec de la lecture des données du fichier';

  @override
  String get fileSizeMustBeLessThan5MB =>
      'La taille du fichier doit être inférieure à 5MB';

  @override
  String get atLeastOnePhoneRequired =>
      'Au moins un numéro de téléphone est requis pour contacter les clients.';

  @override
  String get mobilePhone => 'Téléphone Mobile';

  @override
  String get mobilePhoneRecommended => 'Téléphone Mobile (Recommandé)';

  @override
  String get landlinePhone => 'Téléphone Fixe';

  @override
  String get faxNumber => 'Numéro de Fax';

  @override
  String get mobilePhoneHint => 'ex: +213 555 123 456';

  @override
  String get landlinePhoneHint => 'ex: +213 21 123 456';

  @override
  String get faxNumberHint => 'ex: +213 21 123 457';

  @override
  String get atLeastOnePhoneRequiredValidation =>
      'Au moins un numéro de téléphone est requis';

  @override
  String get currentLocation => 'Localisation Actuelle';

  @override
  String get setBusinessOperatingHours =>
      'Définissez les heures d\'ouverture de votre entreprise pour chaque jour de la semaine';

  @override
  String get primaryLocationDescription =>
      'Ce sera votre emplacement commercial principal. Vous pourrez ajouter d\'autres emplacements plus tard depuis la section de gestion des emplacements';

  @override
  String get currentlyOnlyAvailableInAlgeria =>
      'Actuellement disponible uniquement en Algérie';

  @override
  String get addHours => 'Ajouter des heures';

  @override
  String get enterCompleteAddress => 'Veuillez entrer une adresse complète';

  @override
  String get creditPointsRequiredValidation =>
      'L\'exigence de points de crédit est requise';

  @override
  String get creditPointsPositiveNumber =>
      'Les points de crédit doivent être un nombre positif';

  @override
  String get createYourFirstQueue => 'Créez Votre Première File';

  @override
  String get addAnotherQueue => 'Ajouter Une Autre File';

  @override
  String get createQueueDescription =>
      'Créez au moins une file pour organiser vos services. Chaque file peut avoir ses propres heures d\'ouverture et services assignés';

  @override
  String get createNewQueue => 'Créer Une Nouvelle File';

  @override
  String get assignServices => 'Assigner des Services';

  @override
  String get selectServicesForQueue =>
      'Sélectionnez les services qui seront disponibles dans cette file';

  @override
  String get timeSlots => 'Créneaux Horaires';

  @override
  String get reviewYourSetup => 'Examinez Votre Configuration';

  @override
  String get reviewBusinessInformation =>
      'Veuillez examiner les informations de votre entreprise avant de terminer la configuration';

  @override
  String get businessProfile => 'Profil d\'Entreprise';

  @override
  String get completeSetup => 'Terminer la Configuration';

  @override
  String get nameLabel => 'Nom';

  @override
  String get categoryLabel => 'Catégorie';

  @override
  String get descriptionLabel => 'Description';

  @override
  String get mobileLabel => 'Mobile';

  @override
  String get cityLabel => 'Ville';

  @override
  String get addressLabel => 'Adresse';

  @override
  String get servicesConfigured => 'service(s) configuré(s)';

  @override
  String get readyToCompleteSetup => 'Prêt à Terminer la Configuration';

  @override
  String get businessReadyMessage =>
      'Votre entreprise est prête ! Vous avez configuré votre profil, emplacement, services et files avec les heures d\'ouverture. Cliquez sur \"Terminer la Configuration\" pour finir.';

  @override
  String get queuesConfiguredWithHours =>
      'file(s) configurée(s) avec heures d\'ouverture';

  @override
  String get dashboardShort => 'Accueil';

  @override
  String get calendarShort => 'Agenda';

  @override
  String get customersShort => 'Clients';

  @override
  String get appointmentsShort => 'RDV';
}
