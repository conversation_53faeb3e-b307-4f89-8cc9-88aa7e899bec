import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'notification_service.dart';
import 'notification_api_service.dart';
import 'web_notification_service.dart';
import '../storage/web_storage_service.dart';

/// Service for handling Firebase Cloud Messaging
class FirebaseMessagingService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;
  static bool _tokenSentToServer = false;
  static NotificationApiService? _notificationApiService;

  /// Initialize Firebase Messaging with enhanced debugging
  /// Now initializes immediately like Customer app pattern
  static Future<NotificationSettings?> initialize() async {
    if (_isInitialized) {
      return null;
    }

    try {
      // Request notification permissions
      final settings = await _requestPermissions();

      // Initialize local notifications (only for mobile platforms)
      if (!kIsWeb) {
        await _initializeLocalNotifications();
      } else {}

      // Configure message handlers with debugging
      _configureMessageHandlers();

      // Configure token refresh handler
      _configureTokenRefreshHandler();

      // Get FCM token but don't send to server yet (wait for login)
      try {
        await _getAndStoreFcmToken();
      } catch (tokenError) {
        // Don't fail initialization if token retrieval fails
      }

      // Test notification capability
      await _testNotificationSetup();

      _isInitialized = true;

      // Return the permission settings for external use
      return settings;
    } catch (e) {
      // Don't rethrow - allow app to continue without FCM
      _isInitialized = false;
      return null;
    }
  }

  /// Set the NotificationApiService instance for token management
  static void setNotificationApiService(NotificationApiService apiService) {
    print('[FCM] ===== SETTING NOTIFICATION API SERVICE =====');
    print(
      '[FCM] Previous service was null: ${_notificationApiService == null}',
    );
    _notificationApiService = apiService;
    print('[FCM] NotificationApiService set successfully');
    print('[FCM] Service is now available: ${_notificationApiService != null}');
  }

  /// Test notification setup and capabilities
  static Future<void> _testNotificationSetup() async {
    try {
      // Check if we can get the current token
      final token = await getToken();
      if (token != null) {
      } else {}

      // Check platform capabilities
      if (kIsWeb) {
        // Check if service worker is registered
      } else {
        // Test if we can show a local notification
        await _testLocalNotification();
      }
    } catch (e) {}
  }

  /// Test local notification capability (mobile only)
  static Future<void> _testLocalNotification() async {
    if (kIsWeb) return;

    try {
      // Show a test notification
      await _localNotifications.show(
        999, // Test notification ID
        'FCM Test',
        'If you see this, local notifications are working!',
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'dalti_provider_notifications',
            'Dalti Provider Notifications',
            channelDescription:
                'Default notification channel for Dalti Provider',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
    } catch (e) {}
  }

  /// Request notification permissions
  static Future<NotificationSettings> _requestPermissions() async {
    print('[FCM] ===== REQUESTING NOTIFICATION PERMISSIONS =====');

    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    print('[FCM] Permission Status: ${settings.authorizationStatus}');
    print('[FCM] Alert Setting: ${settings.alert}');
    print('[FCM] Badge Setting: ${settings.badge}');
    print('[FCM] Sound Setting: ${settings.sound}');
    print('[FCM] Announcement Setting: ${settings.announcement}');
    print('[FCM] Car Play Setting: ${settings.carPlay}');
    print('[FCM] Critical Alert Setting: ${settings.criticalAlert}');
    // Note: provisional setting not available in all Firebase versions

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('[FCM] ✅ Notifications AUTHORIZED');
    } else if (settings.authorizationStatus == AuthorizationStatus.denied) {
      print('[FCM] ❌ Notifications DENIED');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.notDetermined) {
      print('[FCM] ⚠️ Notifications NOT DETERMINED');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      print('[FCM] ⚠️ Notifications PROVISIONAL');
    }

    return settings;
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (!kIsWeb) {
      const androidChannel = AndroidNotificationChannel(
        'dalti_provider_notifications',
        'Dalti Provider Notifications',
        description: 'Default notification channel for Dalti Provider',
        importance: Importance.high,
        playSound: true,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(androidChannel);
    }
  }

  /// Configure message handlers with enhanced debugging
  static void _configureMessageHandlers() {
    print('[FCM] ===== CONFIGURING MESSAGE HANDLERS =====');

    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('[FCM] ===== FOREGROUND MESSAGE RECEIVED =====');
      print('[FCM] Message ID: ${message.messageId}');
      print('[FCM] From: ${message.from}');
      print('[FCM] Data: ${message.data}');

      if (message.notification != null) {
        print('[FCM] Notification Title: ${message.notification!.title}');
        print('[FCM] Notification Body: ${message.notification!.body}');
        print('[FCM] Has notification payload: YES');
      } else {
        print('[FCM] Has notification payload: NO (data-only message)');
      }

      _handleForegroundMessage(message);
    });

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('[FCM] ===== MESSAGE OPENED APP FROM BACKGROUND =====');
      print('[FCM] Message ID: ${message.messageId}');
      print('[FCM] From: ${message.from}');
      print('[FCM] Data: ${message.data}');
      if (message.notification != null) {
        print('[FCM] Notification Title: ${message.notification!.title}');
        print('[FCM] Notification Body: ${message.notification!.body}');
      }

      _handleMessageOpenedApp(message);
    });

    // Handle messages when app is opened from terminated state
    _handleInitialMessage();

    print('[FCM] Message handlers configured successfully');
  }

  /// Configure token refresh handler
  static void _configureTokenRefreshHandler() {
    // Listen for token refresh events
    _firebaseMessaging.onTokenRefresh.listen((newToken) {
      // TODO: Update token in backend when it changes
      _handleTokenRefresh(newToken);
    });
  }

  /// Handle token refresh
  static void _handleTokenRefresh(String newToken) async {
    // Store the new token locally
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', newToken);

      // Only send to server if user is logged in (has JWT token)
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(
        'jwt_token',
      );
      if (tokenData != null && tokenData['access_token'] != null) {
        await _sendTokenToServer(newToken);
      } else {}
    } catch (e) {}

    // Also notify the notification service
    NotificationService.handleTokenRefresh(newToken);
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('[FCM] ===== HANDLING FOREGROUND MESSAGE =====');
    print('[FCM] Platform: ${kIsWeb ? "WEB" : "MOBILE"}');

    // Show local notification when app is in foreground (mobile only)
    // Web handles notifications differently through the service worker
    if (!kIsWeb) {
      print('[FCM] Showing local notification for mobile...');
      await _showLocalNotification(message);
      print('[FCM] Local notification shown successfully');
    } else {
      print('[FCM] Showing in-app notification for web...');
      // For web, show an in-app notification
      WebNotificationService.showInAppNotification(
        title: message.notification?.title ?? 'Dalti Provider',
        body: message.notification?.body ?? 'You have a new notification',
        onTap: () => _handleNotificationNavigation(message),
      );
      print('[FCM] In-app notification shown successfully');
    }
  }

  /// Handle initial message when app is opened from terminated state
  static Future<void> _handleInitialMessage() async {
    final message = await _firebaseMessaging.getInitialMessage();
    if (message != null) {
      _handleMessageOpenedApp(message);
    }
  }

  /// Handle messages when app is opened
  static void _handleMessageOpenedApp(RemoteMessage message) {
    // Handle navigation based on message data
    _handleNotificationNavigation(message);
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      const androidDetails = AndroidNotificationDetails(
        'dalti_provider_notifications',
        'Dalti Provider Notifications',
        channelDescription: 'Default notification channel for Dalti Provider',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        notificationDetails,
        payload: jsonEncode(data),
      );
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        _handleNotificationNavigation(RemoteMessage(data: data));
      } catch (e) {}
    }
  }

  /// Handle notification navigation
  static void _handleNotificationNavigation(RemoteMessage message) {
    final data = message.data;

    // Handle different notification types
    final type = data['type'];

    switch (type) {
      case 'appointment':
        _navigateToAppointment(data['appointmentId']);
        break;
      case 'message':
        _navigateToMessages(data['conversationId']);
        break;
      case 'booking':
        _navigateToBooking(data['bookingId']);
        break;
      default:
        _navigateToHome();
    }
  }

  /// Navigate to appointment
  static void _navigateToAppointment(String? appointmentId) {
    // TODO: Implement navigation to appointment screen
  }

  /// Navigate to messages
  static void _navigateToMessages(String? conversationId) {
    // TODO: Implement navigation to messages screen
  }

  /// Navigate to booking
  static void _navigateToBooking(String? bookingId) {
    // TODO: Implement navigation to booking screen
  }

  /// Navigate to home
  static void _navigateToHome() {
    // TODO: Implement navigation to home screen
  }

  /// Get and store FCM token locally (don't send to server yet)
  static Future<void> _getAndStoreFcmToken() async {
    try {
      // Check if we have a stored token first
      final prefs = await SharedPreferences.getInstance();
      String? storedToken = prefs.getString('fcm_token');

      if (storedToken != null && storedToken.isNotEmpty) {
        // Token already exists, no need to regenerate
        //   return;
        return;
      }

      // Get new token
      String? newToken;

      if (kIsWeb) {
        // For web, try to get token with VAPID key
        try {
          // First try without VAPID key (for testing)
          newToken = await _firebaseMessaging.getToken();
          print(
            '[FCM] ✅ Web token obtained without VAPID key: ${newToken?.substring(0, 20)}...',
          );
        } catch (e) {
          print('[FCM] ❌ Failed to get web token without VAPID key: $e');
          // Try with a placeholder VAPID key - you'll need to replace this with your actual key
          try {
            // TODO: Replace with your actual VAPID key from Firebase Console
            // Go to Firebase Console > Project Settings > Cloud Messaging > Web Push certificates
            const vapidKey = 'PLACEHOLDER_VAPID_KEY'; // Replace with actual key
            if (vapidKey != 'PLACEHOLDER_VAPID_KEY') {
              newToken = await _firebaseMessaging.getToken(vapidKey: vapidKey);
              print(
                '[FCM] ✅ Web token obtained with VAPID key: ${newToken?.substring(0, 20)}...',
              );
            } else {
              print(
                '[FCM] ⚠️ VAPID key not configured. Web notifications may not work properly.',
              );
              print(
                '[FCM] To fix: Get VAPID key from Firebase Console > Project Settings > Cloud Messaging',
              );
              return;
            }
          } catch (vapidError) {
            print(
              '[FCM] ❌ Failed to get web token with VAPID key: $vapidError',
            );
            return;
          }
        }
      } else {
        // For mobile platforms
        newToken = await _firebaseMessaging.getToken();
      }

      if (newToken != null) {
        // Store the token locally
        await prefs.setString('fcm_token', newToken);
      } else {}
    } catch (e) {}
  }

  /// Send FCM token to server using NotificationApiService
  static Future<void> _sendTokenToServer(String token) async {
    try {
      print('[FCM] ===== _sendTokenToServer START =====');
      print('[FCM] Token to send: ${token.substring(0, 20)}...');

      // Check if NotificationApiService is available
      if (_notificationApiService == null) {
        print('[FCM] ERROR: NotificationApiService is null');
        print('[FCM] This means the service was not properly initialized');
        throw Exception('NotificationApiService is not available');
      }
      print('[FCM] NotificationApiService is available: YES');

      // Validate authentication state by checking JWT token
      print('[FCM] Checking JWT token in storage...');
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(
        'jwt_token',
      );

      if (tokenData == null) {
        print('[FCM] ERROR: JWT token data is null in storage');
        print('[FCM] User might not be properly authenticated');
        throw Exception('JWT token data not found in storage');
      }
      print('[FCM] JWT token data found in storage: YES');
      print('[FCM] Token data keys: ${tokenData.keys.toList()}');

      final accessToken = tokenData['access_token'] as String?;
      if (accessToken == null || accessToken.isEmpty) {
        print('[FCM] ERROR: Access token is null or empty');
        print('[FCM] Token data: $tokenData');
        throw Exception('Access token is null or empty');
      }
      print('[FCM] Access token found: ${accessToken.substring(0, 20)}...');

      // Use NotificationApiService to send the token
      print('[FCM] Calling NotificationApiService.saveFcmToken...');
      final success = await _notificationApiService!.saveFcmToken(
        fcmToken: token,
      );

      if (success) {
        print('[FCM] ===== _sendTokenToServer SUCCESS =====');
        print('[FCM] FCM token successfully saved to server');
      } else {
        print('[FCM] ERROR: saveFcmToken returned false');
        throw Exception('Failed to save FCM token to server');
      }
    } catch (e) {
      print('[FCM] ===== _sendTokenToServer ERROR =====');
      print('[FCM] Error in _sendTokenToServer: $e');
      print('[FCM] Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Get current FCM token (from storage or fresh from Firebase)
  static Future<String?> getToken() async {
    try {
      // First try to get stored token
      final prefs = await SharedPreferences.getInstance();
      String? storedToken = prefs.getString('fcm_token');

      if (storedToken != null && storedToken.isNotEmpty) {
        return storedToken;
      }

      // If no stored token, try to get fresh token from Firebase

      String? freshToken = await _firebaseMessaging.getToken();

      if (freshToken != null && freshToken.isNotEmpty) {
        // Store the fresh token
        await prefs.setString('fcm_token', freshToken);

        return freshToken;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Request permissions and return the settings
  static Future<NotificationSettings> requestPermissions() async {
    return await _requestPermissions();
  }

  /// Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
    } catch (e) {}
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
    } catch (e) {}
  }

  /// Handle background messages (must be top-level function)
  static Future<void> handleBackgroundMessage(RemoteMessage message) async {
    // Handle background message processing here
  }

  /// Send stored FCM token to server (call after login)
  static Future<void> sendTokenToServerAfterLogin() async {
    try {
      print('[FCM] ===== SEND TOKEN TO SERVER START =====');
      print('[FCM] Attempting to get FCM token...');

      final token = await getToken();
      if (token != null) {
        print(
          '[FCM] FCM token retrieved successfully: ${token.substring(0, 20)}...',
        );
        print('[FCM] Token length: ${token.length}');
        print('[FCM] Calling _sendTokenToServer...');

        await _sendTokenToServer(token);
        _tokenSentToServer = true;

        print(
          '[FCM] Token sent to server successfully, _tokenSentToServer = true',
        );
      } else {
        print('[FCM] ERROR: FCM token is null, cannot send to server');
        throw Exception('FCM token is null');
      }
    } catch (e) {
      print('[FCM] ERROR in sendTokenToServerAfterLogin: $e');
      print('[FCM] Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Re-register FCM token with server (call after login) - Legacy method
  static Future<void> reRegisterToken() async {
    await sendTokenToServerAfterLogin();
  }

  /// Manual debugging method - call this to test FCM setup
  static Future<void> debugFCMSetup() async {
    try {
      print('[FCM] ===== DEBUGGING FCM SETUP =====');

      // Check if FCM is initialized
      print('[FCM] FCM Service Initialized: $_isInitialized');
      print('[FCM] Token Sent to Server: $_tokenSentToServer');
      print(
        '[FCM] NotificationApiService Available: ${_notificationApiService != null}',
      );

      // Check current token
      final token = await getToken();
      if (token != null) {
        print('[FCM] ✅ FCM Token Available: ${token.substring(0, 20)}...');
        print('[FCM] Token Length: ${token.length}');
      } else {
        print('[FCM] ❌ FCM Token is NULL');
      }

      // Check permissions
      final settings = await _firebaseMessaging.getNotificationSettings();
      print('[FCM] Current Permission Status: ${settings.authorizationStatus}');
      print('[FCM] Alert Enabled: ${settings.alert}');
      print('[FCM] Badge Enabled: ${settings.badge}');
      print('[FCM] Sound Enabled: ${settings.sound}');

      // Check JWT token
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(
        'jwt_token',
      );
      if (tokenData != null && tokenData['access_token'] != null) {
        final accessToken = tokenData['access_token'] as String;
        print(
          '[FCM] ✅ JWT Token Available: ${accessToken.substring(0, 20)}...',
        );
      } else {
        print('[FCM] ❌ JWT Token NOT Available');
      }

      print('[FCM] ===== FCM DEBUG COMPLETE =====');
    } catch (e) {
      print('[FCM] ❌ Error during FCM debug: $e');
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await FirebaseMessagingService.handleBackgroundMessage(message);
}
