1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.adscloud.dalti.provider"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:4:5-67
15-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:5:5-79
16-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:5:22-76
17    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Location Services -->
17-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:6:5-76
17-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:6:22-73
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:9:5-79
18-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:10:5-81
19-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- Camera & QR Scanner -->
20-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:11:5-85
20-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:11:22-82
21    <uses-permission android:name="android.permission.CAMERA" />
21-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:14:5-65
21-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:14:22-62
22    <uses-permission android:name="android.permission.FLASHLIGHT" /> <!-- Storage Permissions -->
22-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:15:5-69
22-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:15:22-66
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:18:5-80
23-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:18:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Android 13+ Media Permissions -->
24-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:19:5-81
24-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:19:22-78
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:21:5-76
25-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:21:22-73
26    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
26-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:22:5-75
26-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:22:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- File Management -->
27-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:23:5-75
27-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:23:22-72
28    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- Notifications & Messaging -->
28-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:26:5-82
28-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:26:22-79
29    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
29-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:29:5-77
29-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:29:22-74
30    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
30-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:30:5-79
30-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:30:22-76
31    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
31-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:31:5-74
31-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:31:22-71
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:32:5-68
32-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:32:22-65
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:33:5-66
33-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:33:22-63
34    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Background Services -->
34-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:34:5-81
34-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:34:22-78
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:37:5-77
35-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:37:22-74
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
36-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:38:5-86
36-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:38:22-83
37    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" /> <!-- Phone State (for phone number validation) -->
37-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:39:5-84
37-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:39:22-81
38    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- Firebase Cloud Messaging -->
38-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:42:5-75
38-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:42:22-72
39    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Hardware Features -->
39-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:45:5-82
39-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:45:22-79
40    <uses-feature
40-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:48:5-85
41        android:name="android.hardware.camera"
41-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:48:19-57
42        android:required="false" />
42-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:48:58-82
43    <uses-feature
43-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:49:5-95
44        android:name="android.hardware.camera.autofocus"
44-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:49:19-67
45        android:required="false" />
45-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:49:68-92
46    <uses-feature
46-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:50:5-91
47        android:name="android.hardware.camera.flash"
47-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:50:19-63
48        android:required="false" />
48-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:50:64-88
49    <!--
50 Required to query activities that can process text, see:
51         https://developer.android.com/training/package-visibility and
52         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
53
54         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
55    -->
56    <queries>
56-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:144:5-149:15
57        <intent>
57-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:145:9-148:18
58            <action android:name="android.intent.action.PROCESS_TEXT" />
58-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:146:13-72
58-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:146:21-70
59
60            <data android:mimeType="text/plain" />
60-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:147:13-50
60-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:147:19-48
61        </intent>
62        <intent>
62-->[:file_picker] D:\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
63            <action android:name="android.intent.action.GET_CONTENT" />
63-->[:file_picker] D:\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
63-->[:file_picker] D:\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
64
65            <data android:mimeType="*/*" />
65-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:147:13-50
65-->D:\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:147:19-48
66        </intent>
67    </queries>
68
69    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
69-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
69-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
70    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
70-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
70-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
71    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
71-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
71-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
72    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
72-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
72-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
73
74    <permission
74-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fe99c3dffcb6c3e42d067184fcf556\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
75        android:name="org.adscloud.dalti.provider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
75-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fe99c3dffcb6c3e42d067184fcf556\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
76        android:protectionLevel="signature" />
76-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fe99c3dffcb6c3e42d067184fcf556\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
77
78    <uses-permission android:name="org.adscloud.dalti.provider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
78-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fe99c3dffcb6c3e42d067184fcf556\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
78-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fe99c3dffcb6c3e42d067184fcf556\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
79
80    <application
81        android:name="android.app.Application"
82        android:allowBackup="false"
83        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
83-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fe99c3dffcb6c3e42d067184fcf556\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
84        android:dataExtractionRules="@xml/data_extraction_rules"
85        android:debuggable="true"
86        android:extractNativeLibs="false"
87        android:fullBackupContent="false"
88        android:icon="@mipmap/launcher_icon"
89        android:label="Dalti Provider"
90        android:networkSecurityConfig="@xml/network_security_config"
91        android:requestLegacyExternalStorage="true"
92        android:usesCleartextTraffic="true" >
93        <activity
94            android:name="org.adscloud.dalti.provider.MainActivity"
95            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
96            android:exported="true"
97            android:hardwareAccelerated="true"
98            android:launchMode="singleTop"
99            android:taskAffinity=""
100            android:theme="@style/LaunchTheme"
101            android:windowSoftInputMode="adjustResize" >
102
103            <!--
104                 Specifies an Android theme to apply to this Activity as soon as
105                 the Android process has started. This theme is visible to the user
106                 while the Flutter UI initializes. After that, this theme continues
107                 to determine the Window background behind the Flutter UI.
108            -->
109            <meta-data
110                android:name="io.flutter.embedding.android.NormalTheme"
111                android:resource="@style/NormalTheme" />
112
113            <intent-filter>
114                <action android:name="android.intent.action.MAIN" />
115
116                <category android:name="android.intent.category.LAUNCHER" />
117            </intent-filter>
118            <!-- Intent filter for handling notification clicks -->
119            <intent-filter>
120                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
121
122                <category android:name="android.intent.category.DEFAULT" />
123            </intent-filter>
124        </activity>
125
126        <!-- Local Notifications Service -->
127        <receiver
128            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
129            android:exported="false" />
130        <receiver
131            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
132            android:exported="false" >
133            <intent-filter>
134                <action android:name="android.intent.action.BOOT_COMPLETED" />
135                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
136                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
137                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
138            </intent-filter>
139        </receiver>
140
141        <service
142            android:name="com.google.firebase.messaging.FirebaseMessagingService"
143            android:directBootAware="true"
143-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
144            android:exported="false" >
145            <intent-filter>
145-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
146                <action android:name="com.google.firebase.MESSAGING_EVENT" />
146-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
146-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
147            </intent-filter>
148            <intent-filter android:priority="-500" >
148-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
149                <action android:name="com.google.firebase.MESSAGING_EVENT" />
149-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
149-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
150            </intent-filter>
151        </service>
152        <!-- FCM Notification Configuration -->
153        <meta-data
154            android:name="com.google.firebase.messaging.default_notification_icon"
155            android:resource="@mipmap/launcher_icon" />
156        <meta-data
157            android:name="com.google.firebase.messaging.default_notification_color"
158            android:resource="@color/notification_color" />
159        <meta-data
160            android:name="com.google.firebase.messaging.default_notification_channel_id"
161            android:value="dalti_provider_notifications" />
162
163        <!-- Location Services Configuration -->
164        <meta-data
165            android:name="com.google.android.geo.API_KEY"
166            android:value="YOUR_GOOGLE_MAPS_API_KEY" />
167
168        <!-- File Provider for sharing files -->
169        <provider
170            android:name="androidx.core.content.FileProvider"
171            android:authorities="org.adscloud.dalti.provider.fileprovider"
172            android:exported="false"
173            android:grantUriPermissions="true" >
174            <meta-data
175                android:name="android.support.FILE_PROVIDER_PATHS"
176                android:resource="@xml/file_paths" />
177        </provider>
178
179        <!--
180             Don't delete the meta-data below.
181             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
182        -->
183        <meta-data
184            android:name="flutterEmbedding"
185            android:value="2" />
186
187        <service
187-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
188            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
188-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
189            android:exported="false"
189-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
190            android:permission="android.permission.BIND_JOB_SERVICE" />
190-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
191        <service
191-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
192            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
192-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
193            android:exported="false" >
193-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
194            <intent-filter>
194-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
195                <action android:name="com.google.firebase.MESSAGING_EVENT" />
195-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
195-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
196            </intent-filter>
197        </service>
198
199        <receiver
199-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
200            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
200-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
201            android:exported="true"
201-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
202            android:permission="com.google.android.c2dm.permission.SEND" >
202-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
203            <intent-filter>
203-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
204                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
204-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
204-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
205            </intent-filter>
206        </receiver>
207
208        <service
208-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
209            android:name="com.google.firebase.components.ComponentDiscoveryService"
209-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
210            android:directBootAware="true"
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
211            android:exported="false" >
211-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
212            <meta-data
212-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
213                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
213-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
215            <meta-data
215-->[:firebase_core] D:\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
216                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
216-->[:firebase_core] D:\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[:firebase_core] D:\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
218            <meta-data
218-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
219                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
219-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
221            <meta-data
221-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
222                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
222-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
224            <meta-data
224-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
225                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
225-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96131424616a076c5ad69208c24aae4a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
227            <meta-data
227-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02137f4743114741b46c4d931d12fcb9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
228                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
228-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02137f4743114741b46c4d931d12fcb9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02137f4743114741b46c4d931d12fcb9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
230            <meta-data
230-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02137f4743114741b46c4d931d12fcb9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
231                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
231-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02137f4743114741b46c4d931d12fcb9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02137f4743114741b46c4d931d12fcb9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
233            <meta-data
233-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\126322105c9b5df16f50fb003d04ded1\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
234                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
234-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\126322105c9b5df16f50fb003d04ded1\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
235                android:value="com.google.firebase.components.ComponentRegistrar" />
235-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\126322105c9b5df16f50fb003d04ded1\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
236            <meta-data
236-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
237                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
237-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
238                android:value="com.google.firebase.components.ComponentRegistrar" />
238-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
239            <meta-data
239-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1c402601bca6af60027bfc63ddbf1b2\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
240                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
240-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1c402601bca6af60027bfc63ddbf1b2\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
241                android:value="com.google.firebase.components.ComponentRegistrar" />
241-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1c402601bca6af60027bfc63ddbf1b2\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
242        </service>
243
244        <provider
244-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
245            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
245-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
246            android:authorities="org.adscloud.dalti.provider.flutterfirebasemessaginginitprovider"
246-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
247            android:exported="false"
247-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
248            android:initOrder="99" />
248-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
249
250        <receiver
250-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
251            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
251-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
252            android:exported="true"
252-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
253            android:permission="com.google.android.c2dm.permission.SEND" >
253-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
254            <intent-filter>
254-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
255                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
255-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
255-->[:firebase_messaging] D:\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
256            </intent-filter>
257
258            <meta-data
258-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
259                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
259-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
260                android:value="true" />
260-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf2272c3db1a9b44d31b62c94be6b58\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
261        </receiver>
262
263        <provider
263-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
264            android:name="com.google.firebase.provider.FirebaseInitProvider"
264-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
265            android:authorities="org.adscloud.dalti.provider.firebaseinitprovider"
265-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
266            android:directBootAware="true"
266-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
267            android:exported="false"
267-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
268            android:initOrder="100" />
268-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2662e80c1b1429a555072238d6472fc4\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
269
270        <service
270-->[:geolocator_android] D:\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
271            android:name="com.baseflow.geolocator.GeolocatorLocationService"
271-->[:geolocator_android] D:\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
272            android:enabled="true"
272-->[:geolocator_android] D:\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
273            android:exported="false"
273-->[:geolocator_android] D:\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
274            android:foregroundServiceType="location" />
274-->[:geolocator_android] D:\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
275
276        <activity
276-->[:url_launcher_android] D:\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
277            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
277-->[:url_launcher_android] D:\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
278            android:exported="false"
278-->[:url_launcher_android] D:\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
279            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
279-->[:url_launcher_android] D:\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
280        <!--
281        Service for holding metadata. Cannot be instantiated.
282        Metadata will be merged from other manifests.
283        -->
284        <service
284-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a65a52d0d59569288bb402d53dd272\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:29:9-33:78
285            android:name="androidx.camera.core.impl.MetadataHolderService"
285-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a65a52d0d59569288bb402d53dd272\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:30:13-75
286            android:enabled="false"
286-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a65a52d0d59569288bb402d53dd272\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:31:13-36
287            android:exported="false" >
287-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a65a52d0d59569288bb402d53dd272\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:32:13-37
288            <meta-data
288-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2dc9e415d84d939d93a9d250a899c1e8\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
289                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
289-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2dc9e415d84d939d93a9d250a899c1e8\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
290                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
290-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2dc9e415d84d939d93a9d250a899c1e8\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
291        </service>
292
293        <uses-library
293-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8cf5e82e5c42aeb586ea0c7cf5dbb72\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
294            android:name="androidx.window.extensions"
294-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8cf5e82e5c42aeb586ea0c7cf5dbb72\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
295            android:required="false" />
295-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8cf5e82e5c42aeb586ea0c7cf5dbb72\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
296        <uses-library
296-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8cf5e82e5c42aeb586ea0c7cf5dbb72\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
297            android:name="androidx.window.sidecar"
297-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8cf5e82e5c42aeb586ea0c7cf5dbb72\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
298            android:required="false" />
298-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8cf5e82e5c42aeb586ea0c7cf5dbb72\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
299
300        <receiver
300-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
301            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
301-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
302            android:enabled="true"
302-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
303            android:exported="false" >
303-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
304        </receiver>
305
306        <service
306-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
307            android:name="com.google.android.gms.measurement.AppMeasurementService"
307-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
308            android:enabled="true"
308-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
309            android:exported="false" />
309-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
310        <service
310-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
311            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
311-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
312            android:enabled="true"
312-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
313            android:exported="false"
313-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
314            android:permission="android.permission.BIND_JOB_SERVICE" />
314-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424c5583059b0a38e92d829e333f7b32\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
315        <service
315-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0052fb326440c7f7a9561520a439aa\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
316            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
316-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0052fb326440c7f7a9561520a439aa\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
317            android:directBootAware="true"
317-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
318            android:exported="false" >
318-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0052fb326440c7f7a9561520a439aa\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
319            <meta-data
319-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0052fb326440c7f7a9561520a439aa\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
320                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
320-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0052fb326440c7f7a9561520a439aa\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
321                android:value="com.google.firebase.components.ComponentRegistrar" />
321-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0052fb326440c7f7a9561520a439aa\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
322            <meta-data
322-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1e556409bb2a0485e9533a5d5f49af4\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
323                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
323-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1e556409bb2a0485e9533a5d5f49af4\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
324                android:value="com.google.firebase.components.ComponentRegistrar" />
324-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1e556409bb2a0485e9533a5d5f49af4\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
325            <meta-data
325-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
326                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
326-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
327                android:value="com.google.firebase.components.ComponentRegistrar" />
327-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
328        </service>
329
330        <provider
330-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
331            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
331-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
332            android:authorities="org.adscloud.dalti.provider.mlkitinitprovider"
332-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
333            android:exported="false"
333-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
334            android:initOrder="99" />
334-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\883ab4bfb8dd6a8774f6dff177a9e8de\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
335
336        <activity
336-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfa26447f798317d7f09bc1dbacdadf8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
337            android:name="com.google.android.gms.common.api.GoogleApiActivity"
337-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfa26447f798317d7f09bc1dbacdadf8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
338            android:exported="false"
338-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfa26447f798317d7f09bc1dbacdadf8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
339            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
339-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfa26447f798317d7f09bc1dbacdadf8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
340
341        <provider
341-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
342            android:name="androidx.startup.InitializationProvider"
342-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
343            android:authorities="org.adscloud.dalti.provider.androidx-startup"
343-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
344            android:exported="false" >
344-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
345            <meta-data
345-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
346                android:name="androidx.emoji2.text.EmojiCompatInitializer"
346-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
347                android:value="androidx.startup" />
347-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8ca82cc935e54d64fba2f30e29446c8\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
348            <meta-data
348-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62ea3089c6a520301a71c0c4bda389de\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
349                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
349-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62ea3089c6a520301a71c0c4bda389de\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
350                android:value="androidx.startup" />
350-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62ea3089c6a520301a71c0c4bda389de\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
351            <meta-data
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
352                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
353                android:value="androidx.startup" />
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
354        </provider>
355
356        <uses-library
356-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b78b565c989600f9dde01258cda9c8a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
357            android:name="android.ext.adservices"
357-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b78b565c989600f9dde01258cda9c8a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
358            android:required="false" />
358-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b78b565c989600f9dde01258cda9c8a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
359
360        <meta-data
360-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0fda4cc076273a602c8713a382688ca\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
361            android:name="com.google.android.gms.version"
361-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0fda4cc076273a602c8713a382688ca\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
362            android:value="@integer/google_play_services_version" />
362-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0fda4cc076273a602c8713a382688ca\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
363
364        <service
364-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef09f5e574cf25ce78b33fcde72698\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
365            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
365-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef09f5e574cf25ce78b33fcde72698\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
366            android:exported="false" >
366-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef09f5e574cf25ce78b33fcde72698\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
367            <meta-data
367-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef09f5e574cf25ce78b33fcde72698\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
368                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
368-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef09f5e574cf25ce78b33fcde72698\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
369                android:value="cct" />
369-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef09f5e574cf25ce78b33fcde72698\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
370        </service>
371        <service
371-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
372            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
372-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
373            android:exported="false"
373-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
374            android:permission="android.permission.BIND_JOB_SERVICE" >
374-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
375        </service>
376
377        <receiver
377-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
378            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
378-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
379            android:exported="false" />
379-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97d70b2d003e868061a6f18e66585551\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
380        <receiver
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
381            android:name="androidx.profileinstaller.ProfileInstallReceiver"
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
382            android:directBootAware="false"
382-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
383            android:enabled="true"
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
384            android:exported="true"
384-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
385            android:permission="android.permission.DUMP" >
385-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
386            <intent-filter>
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
387                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
388            </intent-filter>
389            <intent-filter>
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
390                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
391            </intent-filter>
392            <intent-filter>
392-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
393                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
394            </intent-filter>
395            <intent-filter>
395-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
396                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
396-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
396-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33ed251df157decb45a1e8cec1e6d672\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
397            </intent-filter>
398        </receiver>
399    </application>
400
401</manifest>
