/// App routes for Dalti Customer application
/// Centralized route definitions for consistent navigation
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Root routes
  static const String splash = '/';
  static const String welcome = '/welcome';

  // Authentication routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String otpVerification = '/otp-verification';
  static const String verifyResetOtp = '/verify-reset-otp';
  static const String resetPassword = '/reset-password';

  // Main app routes
  static const String home = '/home';
  static const String search = '/search';
  static const String bookings = '/bookings';
  static const String profile = '/profile';

  // Provider routes
  static const String providerDetails = '/provider-details';
  static const String providerServices = '/provider-services';

  // Booking routes
  static const String bookAppointment = '/book-appointment';
  static const String selectService = '/select-service';
  static const String selectDateTime = '/select-date-time';
  static const String selectAddress = '/select-address';
  static const String bookingConfirmation = '/booking-confirmation';
  static const String bookingSuccess = '/booking-success';

  // Appointment routes
  static const String appointmentDetails = '/appointment-details';
  static const String rescheduleAppointment = '/reschedule-appointment';
  static const String cancelAppointment = '/cancel-appointment';
  static const String rateAppointment = '/rate-appointment';

  // Profile routes
  static const String editProfile = '/edit-profile';
  static const String addressManagement = '/address-management';
  static const String addAddress = '/add-address';
  static const String editAddress = '/edit-address';
  static const String paymentMethods = '/payment-methods';
  static const String addPaymentMethod = '/add-payment-method';
  static const String notifications = '/notifications';
  static const String settings = '/settings';

  // Support routes
  static const String help = '/help';
  static const String contactSupport = '/contact-support';
  static const String faq = '/faq';
  static const String termsOfService = '/terms-of-service';
  static const String privacyPolicy = '/privacy-policy';

  // Error routes
  static const String notFound = '/not-found';
  static const String error = '/error';

  /// Get route name from path
  static String getRouteName(String path) {
    switch (path) {
      case splash:
        return 'Splash';
      case welcome:
        return 'Welcome';
      case login:
        return 'Login';
      case register:
        return 'Sign Up';
      case forgotPassword:
        return 'Forgot Password';
      case otpVerification:
        return 'Verify Email';
      case verifyResetOtp:
        return 'Verify Reset Code';
      case resetPassword:
        return 'Reset Password';
      case home:
        return 'Home';
      case search:
        return 'Search';
      case bookings:
        return 'My Bookings';
      case profile:
        return 'Profile';
      case providerDetails:
        return 'Provider Details';
      case providerServices:
        return 'Services';
      case bookAppointment:
        return 'Book Appointment';
      case selectService:
        return 'Select Service';
      case selectDateTime:
        return 'Select Date & Time';
      case selectAddress:
        return 'Select Address';
      case bookingConfirmation:
        return 'Confirm Booking';
      case bookingSuccess:
        return 'Booking Success';
      case appointmentDetails:
        return 'Appointment Details';
      case rescheduleAppointment:
        return 'Reschedule';
      case cancelAppointment:
        return 'Cancel Appointment';
      case rateAppointment:
        return 'Rate Service';
      case editProfile:
        return 'Edit Profile';
      case addressManagement:
        return 'Addresses';
      case addAddress:
        return 'Add Address';
      case editAddress:
        return 'Edit Address';
      case paymentMethods:
        return 'Payment Methods';
      case addPaymentMethod:
        return 'Add Payment Method';
      case notifications:
        return 'Notifications';
      case settings:
        return 'Settings';
      case help:
        return 'Help';
      case contactSupport:
        return 'Contact Support';
      case faq:
        return 'FAQ';
      case termsOfService:
        return 'Terms of Service';
      case privacyPolicy:
        return 'Privacy Policy';
      case notFound:
        return 'Not Found';
      case error:
        return 'Error';
      default:
        return 'Dalti Customer';
    }
  }

  /// Check if route requires authentication
  static bool requiresAuth(String path) {
    const unauthenticatedRoutes = [
      splash,
      welcome,
      login,
      register,
      forgotPassword,
      otpVerification,
      verifyResetOtp,
      resetPassword,
      termsOfService,
      privacyPolicy,
    ];
    
    return !unauthenticatedRoutes.contains(path);
  }

  /// Check if route is an authentication route
  static bool isAuthRoute(String path) {
    const authRoutes = [
      login,
      register,
      forgotPassword,
      otpVerification,
      verifyResetOtp,
      resetPassword,
    ];
    
    return authRoutes.contains(path);
  }

  /// Check if route is a main app route (bottom navigation)
  static bool isMainRoute(String path) {
    const mainRoutes = [
      home,
      search,
      bookings,
      profile,
    ];
    
    return mainRoutes.contains(path);
  }

  /// Get bottom navigation index for main routes
  static int getBottomNavIndex(String path) {
    switch (path) {
      case home:
        return 0;
      case search:
        return 1;
      case bookings:
        return 2;
      case profile:
        return 3;
      default:
        return 0;
    }
  }

  /// Get main route from bottom navigation index
  static String getMainRouteFromIndex(int index) {
    switch (index) {
      case 0:
        return home;
      case 1:
        return search;
      case 2:
        return bookings;
      case 3:
        return profile;
      default:
        return home;
    }
  }

  /// All routes list for validation
  static const List<String> allRoutes = [
    splash,
    welcome,
    login,
    register,
    forgotPassword,
    otpVerification,
    verifyResetOtp,
    resetPassword,
    home,
    search,
    bookings,
    profile,
    providerDetails,
    providerServices,
    bookAppointment,
    selectService,
    selectDateTime,
    selectAddress,
    bookingConfirmation,
    bookingSuccess,
    appointmentDetails,
    rescheduleAppointment,
    cancelAppointment,
    rateAppointment,
    editProfile,
    addressManagement,
    addAddress,
    editAddress,
    paymentMethods,
    addPaymentMethod,
    notifications,
    settings,
    help,
    contactSupport,
    faq,
    termsOfService,
    privacyPolicy,
    notFound,
    error,
  ];
}
