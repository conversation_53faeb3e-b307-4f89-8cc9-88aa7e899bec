// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationItemImpl _$$NotificationItemImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationItemImpl(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      userId: json['userId'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      isRead: json['isRead'] as bool,
      readAt: json['readAt'] == null
          ? null
          : DateTime.parse(json['readAt'] as String),
      link: json['link'] as String?,
      actorId: json['actorId'] as String?,
      actor: json['actor'] == null
          ? null
          : NotificationActor.fromJson(json['actor'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$NotificationItemImplToJson(
    _$NotificationItemImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'createdAt': instance.createdAt.toIso8601String(),
    'updatedAt': instance.updatedAt.toIso8601String(),
    'userId': instance.userId,
    'type': instance.type,
    'title': instance.title,
    'message': instance.message,
    'isRead': instance.isRead,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('readAt', instance.readAt?.toIso8601String());
  writeNotNull('link', instance.link);
  writeNotNull('actorId', instance.actorId);
  writeNotNull('actor', instance.actor?.toJson());
  return val;
}

_$NotificationActorImpl _$$NotificationActorImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationActorImpl(
      id: json['id'] as String,
      username: json['username'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$$NotificationActorImplToJson(
    _$NotificationActorImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('username', instance.username);
  writeNotNull('firstName', instance.firstName);
  writeNotNull('lastName', instance.lastName);
  return val;
}
