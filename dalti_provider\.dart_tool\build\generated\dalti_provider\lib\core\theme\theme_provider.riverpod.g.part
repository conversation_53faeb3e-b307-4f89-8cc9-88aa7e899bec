// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentThemeHash() => r'2c4ace1450f3622c933563ed7b7eaa915a0df6e4';

/// Provider for accessing current theme data
///
/// Copied from [currentTheme].
@ProviderFor(currentTheme)
final currentThemeProvider = AutoDisposeProvider<ThemeData>.internal(
  currentTheme,
  name: r'currentThemeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentThemeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentThemeRef = AutoDisposeProviderRef<ThemeData>;
String _$themeNotifierHash() => r'7782c31c6485c2833247231581d6c4cdbeea1b53';

/// Theme provider for managing app theme state
///
/// Copied from [ThemeNotifier].
@ProviderFor(ThemeNotifier)
final themeNotifierProvider =
    AutoDisposeNotifierProvider<ThemeNotifier, ThemeState>.internal(
  ThemeNotifier.new,
  name: r'themeNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$themeNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ThemeNotifier = AutoDisposeNotifier<ThemeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
