import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Firebase configuration for different environments
class FirebaseConfig {
  /// Get Firebase options based on current environment
  /// Now using dalti-prod for all environments (dev and prod)
  static FirebaseOptions get currentPlatform {
    return _getProductionOptions(); // Always use production Firebase project
  }

  /// Production Firebase options (dalti-prod project for all environments)
  static FirebaseOptions _getProductionOptions() {
    if (kIsWeb) {
      return const FirebaseOptions(
        apiKey: "AIzaSyC946kqiZ2Eoc88vslGXFxVa-6yrWicVec",
        authDomain: "dalti-prod.firebaseapp.com",
        projectId: "dalti-prod",
        storageBucket: "dalti-prod.firebasestorage.app",
        messagingSenderId: "1060372851323",
        appId: "1:1060372851323:web:690318c8147b5c8a0690de",
        measurementId: "G-Q0BSTXW5FL",
      );
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return const FirebaseOptions(
          apiKey: "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg",
          appId: "1:1060372851323:android:c968a0882c726c190690de",
          messagingSenderId: "1060372851323",
          projectId: "dalti-prod",
          storageBucket: "dalti-prod.firebasestorage.app",
        );
      case TargetPlatform.iOS:
        return const FirebaseOptions(
          apiKey: "AIzaSyCkwDt0Tg-1VIm8SMfaaDmwfCE36U91Bko",
          appId: "1:1060372851323:ios:5048ecd5ea260b5e0690de",
          messagingSenderId: "1060372851323",
          projectId: "dalti-prod",
          storageBucket: "dalti-prod.firebasestorage.app",
          iosBundleId: "org.adscloud.dalti.provider",
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for this platform.',
        );
    }
  }
}
