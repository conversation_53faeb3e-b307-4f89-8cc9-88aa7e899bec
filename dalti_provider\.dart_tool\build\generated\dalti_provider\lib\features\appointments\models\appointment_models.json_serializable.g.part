// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppointmentImpl _$$AppointmentImplFromJson(Map<String, dynamic> json) =>
    _$AppointmentImpl(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      customerPhone: json['customerPhone'] as String?,
      customerEmail: json['customerEmail'] as String?,
      serviceId: json['serviceId'] as String,
      serviceName: json['serviceName'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      expectedEndTime: json['expectedEndTime'] == null
          ? null
          : DateTime.parse(json['expectedEndTime'] as String),
      duration: (json['duration'] as num).toInt(),
      status: $enumDecode(_$AppointmentStatusEnumMap, json['status']),
      locationId: json['locationId'] as String?,
      locationName: json['locationName'] as String?,
      queueId: json['queueId'] as String?,
      queueName: json['queueName'] as String?,
      providerId: json['providerId'] as String?,
      providerName: json['providerName'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      cancellationReason: json['cancellationReason'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      realAppointmentStartTime: json['realAppointmentStartTime'] == null
          ? null
          : DateTime.parse(json['realAppointmentStartTime'] as String),
      realAppointmentEndTime: json['realAppointmentEndTime'] == null
          ? null
          : DateTime.parse(json['realAppointmentEndTime'] as String),
      reminderSent: json['reminderSent'] as String?,
      isRecurring: json['isRecurring'] as bool?,
      recurringPattern: json['recurringPattern'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$AppointmentImplToJson(_$AppointmentImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'customerId': instance.customerId,
    'customerName': instance.customerName,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('customerPhone', instance.customerPhone);
  writeNotNull('customerEmail', instance.customerEmail);
  val['serviceId'] = instance.serviceId;
  val['serviceName'] = instance.serviceName;
  val['scheduledTime'] = instance.scheduledTime.toIso8601String();
  writeNotNull('expectedEndTime', instance.expectedEndTime?.toIso8601String());
  val['duration'] = instance.duration;
  val['status'] = _$AppointmentStatusEnumMap[instance.status]!;
  writeNotNull('locationId', instance.locationId);
  writeNotNull('locationName', instance.locationName);
  writeNotNull('queueId', instance.queueId);
  writeNotNull('queueName', instance.queueName);
  writeNotNull('providerId', instance.providerId);
  writeNotNull('providerName', instance.providerName);
  writeNotNull('price', instance.price);
  writeNotNull('notes', instance.notes);
  writeNotNull('cancellationReason', instance.cancellationReason);
  writeNotNull('createdAt', instance.createdAt?.toIso8601String());
  writeNotNull('updatedAt', instance.updatedAt?.toIso8601String());
  writeNotNull('completedAt', instance.completedAt?.toIso8601String());
  writeNotNull('realAppointmentStartTime',
      instance.realAppointmentStartTime?.toIso8601String());
  writeNotNull('realAppointmentEndTime',
      instance.realAppointmentEndTime?.toIso8601String());
  writeNotNull('reminderSent', instance.reminderSent);
  writeNotNull('isRecurring', instance.isRecurring);
  writeNotNull('recurringPattern', instance.recurringPattern);
  writeNotNull('tags', instance.tags);
  return val;
}

const _$AppointmentStatusEnumMap = {
  AppointmentStatus.pending: 'pending',
  AppointmentStatus.scheduled: 'scheduled',
  AppointmentStatus.confirmed: 'confirmed',
  AppointmentStatus.inProgress: 'InProgress',
  AppointmentStatus.completed: 'completed',
  AppointmentStatus.canceled: 'canceled',
  AppointmentStatus.noShow: 'no_show',
  AppointmentStatus.rescheduled: 'rescheduled',
};

_$AppointmentRequestImpl _$$AppointmentRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$AppointmentRequestImpl(
      customerId: json['customerId'] as String,
      serviceId: json['serviceId'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      duration: (json['duration'] as num).toInt(),
      locationId: json['locationId'] as String?,
      queueId: json['queueId'] as String?,
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$AppointmentRequestImplToJson(
    _$AppointmentRequestImpl instance) {
  final val = <String, dynamic>{
    'customerId': instance.customerId,
    'serviceId': instance.serviceId,
    'scheduledTime': instance.scheduledTime.toIso8601String(),
    'duration': instance.duration,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('locationId', instance.locationId);
  writeNotNull('queueId', instance.queueId);
  writeNotNull('notes', instance.notes);
  writeNotNull('tags', instance.tags);
  return val;
}

_$AppointmentSearchFiltersImpl _$$AppointmentSearchFiltersImplFromJson(
        Map<String, dynamic> json) =>
    _$AppointmentSearchFiltersImpl(
      customerName: json['customerName'] as String?,
      serviceName: json['serviceName'] as String?,
      locationId: json['locationId'] as String?,
      queueId: json['queueId'] as String?,
      status: $enumDecodeNullable(_$AppointmentStatusEnumMap, json['status']),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$AppointmentSearchFiltersImplToJson(
    _$AppointmentSearchFiltersImpl instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('customerName', instance.customerName);
  writeNotNull('serviceName', instance.serviceName);
  writeNotNull('locationId', instance.locationId);
  writeNotNull('queueId', instance.queueId);
  writeNotNull('status', _$AppointmentStatusEnumMap[instance.status]);
  writeNotNull('startDate', instance.startDate?.toIso8601String());
  writeNotNull('endDate', instance.endDate?.toIso8601String());
  writeNotNull('tags', instance.tags);
  return val;
}

_$AppointmentResponseImpl _$$AppointmentResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AppointmentResponseImpl(
      success: json['success'] as bool,
      appointment: json['appointment'] == null
          ? null
          : Appointment.fromJson(json['appointment'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : AppointmentError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppointmentResponseImplToJson(
    _$AppointmentResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('appointment', instance.appointment?.toJson());
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$AppointmentsResponseImpl _$$AppointmentsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AppointmentsResponseImpl(
      success: json['success'] as bool,
      appointments: (json['appointments'] as List<dynamic>?)
              ?.map((e) => Appointment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 20,
      error: json['error'] == null
          ? null
          : AppointmentError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppointmentsResponseImplToJson(
    _$AppointmentsResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
    'appointments': instance.appointments.map((e) => e.toJson()).toList(),
    'totalCount': instance.totalCount,
    'currentPage': instance.currentPage,
    'pageSize': instance.pageSize,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$AppointmentErrorImpl _$$AppointmentErrorImplFromJson(
        Map<String, dynamic> json) =>
    _$AppointmentErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
    );

Map<String, dynamic> _$$AppointmentErrorImplToJson(
    _$AppointmentErrorImpl instance) {
  final val = <String, dynamic>{
    'code': instance.code,
    'message': instance.message,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('details', instance.details);
  return val;
}

_$CalendarViewDataImpl _$$CalendarViewDataImplFromJson(
        Map<String, dynamic> json) =>
    _$CalendarViewDataImpl(
      date: DateTime.parse(json['date'] as String),
      appointments: (json['appointments'] as List<dynamic>?)
              ?.map((e) => Appointment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalAppointments: (json['totalAppointments'] as num?)?.toInt() ?? 0,
      completedAppointments:
          (json['completedAppointments'] as num?)?.toInt() ?? 0,
      cancelledAppointments:
          (json['cancelledAppointments'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CalendarViewDataImplToJson(
        _$CalendarViewDataImpl instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'appointments': instance.appointments.map((e) => e.toJson()).toList(),
      'totalAppointments': instance.totalAppointments,
      'completedAppointments': instance.completedAppointments,
      'cancelledAppointments': instance.cancelledAppointments,
    };

_$TimeSlotImpl _$$TimeSlotImplFromJson(Map<String, dynamic> json) =>
    _$TimeSlotImpl(
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      isAvailable: json['isAvailable'] as bool,
      appointmentId: json['appointmentId'] as String?,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$$TimeSlotImplToJson(_$TimeSlotImpl instance) {
  final val = <String, dynamic>{
    'startTime': instance.startTime.toIso8601String(),
    'endTime': instance.endTime.toIso8601String(),
    'isAvailable': instance.isAvailable,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('appointmentId', instance.appointmentId);
  writeNotNull('reason', instance.reason);
  return val;
}

_$AppointmentStatsImpl _$$AppointmentStatsImplFromJson(
        Map<String, dynamic> json) =>
    _$AppointmentStatsImpl(
      totalAppointments: (json['totalAppointments'] as num?)?.toInt() ?? 0,
      scheduledAppointments:
          (json['scheduledAppointments'] as num?)?.toInt() ?? 0,
      completedAppointments:
          (json['completedAppointments'] as num?)?.toInt() ?? 0,
      cancelledAppointments:
          (json['cancelledAppointments'] as num?)?.toInt() ?? 0,
      noShowAppointments: (json['noShowAppointments'] as num?)?.toInt() ?? 0,
      completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
      cancellationRate: (json['cancellationRate'] as num?)?.toDouble() ?? 0.0,
      noShowRate: (json['noShowRate'] as num?)?.toDouble() ?? 0.0,
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$AppointmentStatsImplToJson(
        _$AppointmentStatsImpl instance) =>
    <String, dynamic>{
      'totalAppointments': instance.totalAppointments,
      'scheduledAppointments': instance.scheduledAppointments,
      'completedAppointments': instance.completedAppointments,
      'cancelledAppointments': instance.cancelledAppointments,
      'noShowAppointments': instance.noShowAppointments,
      'completionRate': instance.completionRate,
      'cancellationRate': instance.cancellationRate,
      'noShowRate': instance.noShowRate,
      'totalRevenue': instance.totalRevenue,
    };

_$CreateAppointmentRequestImpl _$$CreateAppointmentRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateAppointmentRequestImpl(
      customerUserId: json['customerUserId'] as String,
      serviceId: (json['serviceId'] as num).toInt(),
      placeId: (json['placeId'] as num).toInt(),
      queueId: (json['queueId'] as num).toInt(),
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$CreateAppointmentRequestImplToJson(
    _$CreateAppointmentRequestImpl instance) {
  final val = <String, dynamic>{
    'customerUserId': instance.customerUserId,
    'serviceId': instance.serviceId,
    'placeId': instance.placeId,
    'queueId': instance.queueId,
    'startTime': instance.startTime.toIso8601String(),
    'endTime': instance.endTime.toIso8601String(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('notes', instance.notes);
  return val;
}

_$UpdateAppointmentRequestImpl _$$UpdateAppointmentRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateAppointmentRequestImpl(
      serviceId: (json['serviceId'] as num).toInt(),
      queueId: (json['queueId'] as num).toInt(),
      expectedStartTime: json['expectedStartTime'] as String,
      expectedEndTime: json['expectedEndTime'] as String,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$UpdateAppointmentRequestImplToJson(
    _$UpdateAppointmentRequestImpl instance) {
  final val = <String, dynamic>{
    'serviceId': instance.serviceId,
    'queueId': instance.queueId,
    'expectedStartTime': instance.expectedStartTime,
    'expectedEndTime': instance.expectedEndTime,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('notes', instance.notes);
  return val;
}
