// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BusinessMetricsImpl _$$BusinessMetricsImplFromJson(
        Map<String, dynamic> json) =>
    _$BusinessMetricsImpl(
      todayRevenue: (json['todayRevenue'] as num?)?.toDouble() ?? 0.0,
      todayAppointments: (json['todayAppointments'] as num?)?.toInt() ?? 0,
      completedAppointments:
          (json['completedAppointments'] as num?)?.toInt() ?? 0,
      cancelledAppointments:
          (json['cancelledAppointments'] as num?)?.toInt() ?? 0,
      activeQueues: (json['activeQueues'] as num?)?.toInt() ?? 0,
      totalCustomersToday: (json['totalCustomersToday'] as num?)?.toInt() ?? 0,
      customerSatisfaction:
          (json['customerSatisfaction'] as num?)?.toDouble() ?? 0.0,
      averageWaitTime: (json['averageWaitTime'] as num?)?.toDouble() ?? 0.0,
      weeklyRevenueData: (json['weeklyRevenueData'] as List<dynamic>?)
              ?.map((e) => (e as num).toDouble())
              .toList() ??
          const [],
      weeklyAppointmentData: (json['weeklyAppointmentData'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      revenueChange: (json['revenueChange'] as num?)?.toDouble() ?? 0.0,
      appointmentsChange:
          (json['appointmentsChange'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$BusinessMetricsImplToJson(
        _$BusinessMetricsImpl instance) =>
    <String, dynamic>{
      'todayRevenue': instance.todayRevenue,
      'todayAppointments': instance.todayAppointments,
      'completedAppointments': instance.completedAppointments,
      'cancelledAppointments': instance.cancelledAppointments,
      'activeQueues': instance.activeQueues,
      'totalCustomersToday': instance.totalCustomersToday,
      'customerSatisfaction': instance.customerSatisfaction,
      'averageWaitTime': instance.averageWaitTime,
      'weeklyRevenueData': instance.weeklyRevenueData,
      'weeklyAppointmentData': instance.weeklyAppointmentData,
      'revenueChange': instance.revenueChange,
      'appointmentsChange': instance.appointmentsChange,
    };

_$ScheduleDataImpl _$$ScheduleDataImplFromJson(Map<String, dynamic> json) =>
    _$ScheduleDataImpl(
      nextAppointment: json['nextAppointment'] == null
          ? null
          : NextAppointment.fromJson(
              json['nextAppointment'] as Map<String, dynamic>),
      queueStatuses: (json['queueStatuses'] as List<dynamic>?)
              ?.map((e) => QueueStatus.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      todayHours: json['todayHours'] == null
          ? null
          : WorkingHours.fromJson(json['todayHours'] as Map<String, dynamic>),
      totalAppointments: (json['totalAppointments'] as num?)?.toInt() ?? 0,
      completedAppointments:
          (json['completedAppointments'] as num?)?.toInt() ?? 0,
      upcomingAppointments:
          (json['upcomingAppointments'] as num?)?.toInt() ?? 0,
      cancelledAppointments:
          (json['cancelledAppointments'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$ScheduleDataImplToJson(_$ScheduleDataImpl instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('nextAppointment', instance.nextAppointment?.toJson());
  val['queueStatuses'] = instance.queueStatuses.map((e) => e.toJson()).toList();
  writeNotNull('todayHours', instance.todayHours?.toJson());
  val['totalAppointments'] = instance.totalAppointments;
  val['completedAppointments'] = instance.completedAppointments;
  val['upcomingAppointments'] = instance.upcomingAppointments;
  val['cancelledAppointments'] = instance.cancelledAppointments;
  return val;
}

_$NextAppointmentImpl _$$NextAppointmentImplFromJson(
        Map<String, dynamic> json) =>
    _$NextAppointmentImpl(
      id: json['id'] as String,
      customerName: json['customerName'] as String,
      serviceName: json['serviceName'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt() ?? 30,
      status: json['status'] as String? ?? 'confirmed',
    );

Map<String, dynamic> _$$NextAppointmentImplToJson(
        _$NextAppointmentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerName': instance.customerName,
      'serviceName': instance.serviceName,
      'scheduledTime': instance.scheduledTime.toIso8601String(),
      'estimatedDuration': instance.estimatedDuration,
      'status': instance.status,
    };

_$QueueStatusImpl _$$QueueStatusImplFromJson(Map<String, dynamic> json) =>
    _$QueueStatusImpl(
      queueId: json['queueId'] as String,
      queueName: json['queueName'] as String,
      locationName: json['locationName'] as String,
      waitingCount: (json['waitingCount'] as num?)?.toInt() ?? 0,
      averageWaitTime: (json['averageWaitTime'] as num?)?.toDouble() ?? 0.0,
      isActive: json['isActive'] as bool? ?? true,
      nextAvailableSlot: json['nextAvailableSlot'] == null
          ? null
          : DateTime.parse(json['nextAvailableSlot'] as String),
    );

Map<String, dynamic> _$$QueueStatusImplToJson(_$QueueStatusImpl instance) {
  final val = <String, dynamic>{
    'queueId': instance.queueId,
    'queueName': instance.queueName,
    'locationName': instance.locationName,
    'waitingCount': instance.waitingCount,
    'averageWaitTime': instance.averageWaitTime,
    'isActive': instance.isActive,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull(
      'nextAvailableSlot', instance.nextAvailableSlot?.toIso8601String());
  return val;
}

_$WorkingHoursImpl _$$WorkingHoursImplFromJson(Map<String, dynamic> json) =>
    _$WorkingHoursImpl(
      openTime: json['openTime'] as String,
      closeTime: json['closeTime'] as String,
      isOpen: json['isOpen'] as bool? ?? true,
      breakTimes: (json['breakTimes'] as List<dynamic>?)
              ?.map((e) => BreakTime.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$WorkingHoursImplToJson(_$WorkingHoursImpl instance) =>
    <String, dynamic>{
      'openTime': instance.openTime,
      'closeTime': instance.closeTime,
      'isOpen': instance.isOpen,
      'breakTimes': instance.breakTimes.map((e) => e.toJson()).toList(),
    };

_$BreakTimeImpl _$$BreakTimeImplFromJson(Map<String, dynamic> json) =>
    _$BreakTimeImpl(
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      description: json['description'] as String,
    );

Map<String, dynamic> _$$BreakTimeImplToJson(_$BreakTimeImpl instance) =>
    <String, dynamic>{
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'description': instance.description,
    };

_$NotificationItemImpl _$$NotificationItemImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationItemImpl(
      id: json['id'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      title: json['title'] as String,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
      priority: json['priority'] as String? ?? 'medium',
      actionData: json['actionData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$NotificationItemImplToJson(
    _$NotificationItemImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'type': _$NotificationTypeEnumMap[instance.type]!,
    'title': instance.title,
    'message': instance.message,
    'timestamp': instance.timestamp.toIso8601String(),
    'isRead': instance.isRead,
    'priority': instance.priority,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('actionData', instance.actionData);
  return val;
}

const _$NotificationTypeEnumMap = {
  NotificationType.rescheduleRequest: 'reschedule_request',
  NotificationType.queueAlert: 'queue_alert',
  NotificationType.newCustomer: 'new_customer',
  NotificationType.appointmentReminder: 'appointment_reminder',
  NotificationType.systemNotification: 'system_notification',
  NotificationType.emergencyAlert: 'emergency_alert',
};

_$QuickStatsImpl _$$QuickStatsImplFromJson(Map<String, dynamic> json) =>
    _$QuickStatsImpl(
      currentWaitingCustomers:
          (json['currentWaitingCustomers'] as num?)?.toInt() ?? 0,
      activeQueues: (json['activeQueues'] as num?)?.toInt() ?? 0,
      todayRevenue: (json['todayRevenue'] as num?)?.toDouble() ?? 0.0,
      unreadNotifications: (json['unreadNotifications'] as num?)?.toInt() ?? 0,
      nextAppointmentIn: (json['nextAppointmentIn'] as num?)?.toInt() ?? 0,
      averageWaitTime: (json['averageWaitTime'] as num?)?.toDouble() ?? 0.0,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$$QuickStatsImplToJson(_$QuickStatsImpl instance) {
  final val = <String, dynamic>{
    'currentWaitingCustomers': instance.currentWaitingCustomers,
    'activeQueues': instance.activeQueues,
    'todayRevenue': instance.todayRevenue,
    'unreadNotifications': instance.unreadNotifications,
    'nextAppointmentIn': instance.nextAppointmentIn,
    'averageWaitTime': instance.averageWaitTime,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('lastUpdated', instance.lastUpdated?.toIso8601String());
  return val;
}

_$DashboardDataImpl _$$DashboardDataImplFromJson(Map<String, dynamic> json) =>
    _$DashboardDataImpl(
      businessMetrics: json['businessMetrics'] == null
          ? null
          : BusinessMetrics.fromJson(
              json['businessMetrics'] as Map<String, dynamic>),
      scheduleData: json['scheduleData'] == null
          ? null
          : ScheduleData.fromJson(json['scheduleData'] as Map<String, dynamic>),
      notifications: (json['notifications'] as List<dynamic>?)
              ?.map((e) => NotificationItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      quickStats: json['quickStats'] == null
          ? null
          : QuickStats.fromJson(json['quickStats'] as Map<String, dynamic>),
      unreadNotificationCount:
          (json['unreadNotificationCount'] as num?)?.toInt() ?? 0,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$$DashboardDataImplToJson(_$DashboardDataImpl instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('businessMetrics', instance.businessMetrics?.toJson());
  writeNotNull('scheduleData', instance.scheduleData?.toJson());
  val['notifications'] = instance.notifications.map((e) => e.toJson()).toList();
  writeNotNull('quickStats', instance.quickStats?.toJson());
  val['unreadNotificationCount'] = instance.unreadNotificationCount;
  writeNotNull('lastUpdated', instance.lastUpdated?.toIso8601String());
  return val;
}

_$BusinessMetricsResponseImpl _$$BusinessMetricsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$BusinessMetricsResponseImpl(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : BusinessMetrics.fromJson(json['data'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : DashboardError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$BusinessMetricsResponseImplToJson(
    _$BusinessMetricsResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('data', instance.data?.toJson());
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$ScheduleDataResponseImpl _$$ScheduleDataResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ScheduleDataResponseImpl(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : ScheduleData.fromJson(json['data'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : DashboardError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ScheduleDataResponseImplToJson(
    _$ScheduleDataResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('data', instance.data?.toJson());
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$NotificationsResponseImpl _$$NotificationsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationsResponseImpl(
      success: json['success'] as bool,
      notifications: (json['notifications'] as List<dynamic>?)
              ?.map((e) => NotificationItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      unreadCount: (json['unreadCount'] as num?)?.toInt() ?? 0,
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      error: json['error'] == null
          ? null
          : DashboardError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$NotificationsResponseImplToJson(
    _$NotificationsResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
    'notifications': instance.notifications.map((e) => e.toJson()).toList(),
    'unreadCount': instance.unreadCount,
    'totalCount': instance.totalCount,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$QuickStatsResponseImpl _$$QuickStatsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$QuickStatsResponseImpl(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : QuickStats.fromJson(json['data'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : DashboardError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$QuickStatsResponseImplToJson(
    _$QuickStatsResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('data', instance.data?.toJson());
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$BasicResponseImpl _$$BasicResponseImplFromJson(Map<String, dynamic> json) =>
    _$BasicResponseImpl(
      success: json['success'] as bool,
      message: json['message'] as String?,
      error: json['error'] == null
          ? null
          : DashboardError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$BasicResponseImplToJson(_$BasicResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('message', instance.message);
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$EmergencyControlResponseImpl _$$EmergencyControlResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$EmergencyControlResponseImpl(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : EmergencyQueueControlResponse.fromJson(
              json['data'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : DashboardError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$EmergencyControlResponseImplToJson(
    _$EmergencyControlResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('message', instance.message);
  writeNotNull('data', instance.data?.toJson());
  writeNotNull('error', instance.error?.toJson());
  return val;
}

_$DashboardErrorImpl _$$DashboardErrorImplFromJson(Map<String, dynamic> json) =>
    _$DashboardErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
    );

Map<String, dynamic> _$$DashboardErrorImplToJson(
    _$DashboardErrorImpl instance) {
  final val = <String, dynamic>{
    'code': instance.code,
    'message': instance.message,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('details', instance.details);
  return val;
}

_$EmergencyQueueControlImpl _$$EmergencyQueueControlImplFromJson(
        Map<String, dynamic> json) =>
    _$EmergencyQueueControlImpl(
      action: json['action'] as String,
      reason: json['reason'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$EmergencyQueueControlImplToJson(
    _$EmergencyQueueControlImpl instance) {
  final val = <String, dynamic>{
    'action': instance.action,
    'reason': instance.reason,
    'timestamp': instance.timestamp.toIso8601String(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('estimatedDuration', instance.estimatedDuration);
  return val;
}

_$EmergencyQueueControlResponseImpl
    _$$EmergencyQueueControlResponseImplFromJson(Map<String, dynamic> json) =>
        _$EmergencyQueueControlResponseImpl(
          success: json['success'] as bool,
          message: json['message'] as String,
          affectedQueues: (json['affectedQueues'] as num?)?.toInt() ?? 0,
          estimatedResumeTime: json['estimatedResumeTime'] == null
              ? null
              : DateTime.parse(json['estimatedResumeTime'] as String),
        );

Map<String, dynamic> _$$EmergencyQueueControlResponseImplToJson(
    _$EmergencyQueueControlResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
    'message': instance.message,
    'affectedQueues': instance.affectedQueues,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull(
      'estimatedResumeTime', instance.estimatedResumeTime?.toIso8601String());
  return val;
}
